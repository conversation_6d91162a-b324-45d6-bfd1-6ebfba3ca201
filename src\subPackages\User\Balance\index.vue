<template>
  <view class="balance">
    <view
      class="banlance-header"
      :style="{ backgroundImage: `url(${balanceBg})` }"
    >
      <view class="banlance-header-number">
        <view class="text">账户余额</view>
        <view class="number">0.00</view>
      </view>
      <view class="banlance-header-btn">
        <van-button
          class="btn"
          size="small"
          round
          color="linear-gradient( 303deg, #77ACFF 0%, #C7DBFF 100%)"
          customStyle="padding: 0 20rpx;"
          @click="showPayPopup=true"
        >
          充值
        </van-button>
      </view>
    </view>

    <view class="banlance-content">
      <van-tabs
        color="#1677FF"
        title-active-color="#1677FF"
        title-inactive-color="#666666"
        line-height="4rpx"
        :active="orderType"
        sticky
        @change="tabsChange"
        animated
        use-before-change
        @beforeChange="onBeforeChange"
        swipeable
        line-width="20"
      >
        <van-tab
          v-for="(item, index) in tabs"
          :key="index"
          :name="item.status"
          :title="item.title"
        >
          <balanceCard />
          <balanceCard />
          <view class="emptyBox" v-if="orderListRef.length === 0 && !loading">
            <image :src="balanceEmpty" class="emptyImg" alt="" />
            <view class="emptyNotice">暂无数据</view>
          </view>
        </van-tab>
      </van-tabs>
    </view>
    <balancePay v-model:show="showPayPopup" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import balanceBg from "@/static/images/user/balance.png";
import balanceEmpty from "@/static/images/user/balanceEmpty.png";
import balanceCard from "./components/balanceCard.vue"
import balancePay from "./components/balancePay.vue"
const enum BalanceTabsEnum {
  /** 支出 */
  Expenditure = 1,
  /** 收入 */
  Income = 2,
}

const showPayPopup = ref<boolean>(false)

const orderType = ref<BalanceTabsEnum>(BalanceTabsEnum.Expenditure);
const tabs = [
  {
    title: "支出",
    status: BalanceTabsEnum.Expenditure,
  },
  {
    title: "收入",
    status: BalanceTabsEnum.Income,
  },
];
const pageVO = reactive({
  current: 1,
  size: 10,
});
const total = ref(0);
const orderListRef = ref([]);
const loading = ref(false);
const tabsChange = (e) => {
  orderType.value = e.detail.name;
  pageVO.current = 1;
};

const onBeforeChange = (event) => {
  const { callback, title, name } = event.detail;
  pageVO.current = 1;
  total.value = 0;
  getOrderList({ orderStatus: name, isClear: true }).then(() => {
    callback(true);
  });
  // setTimeout(() => {
  //   uni.hideLoading();
  //   callback(true);
  // }, 2000);
};

const getOrderList = (
  params = { orderStatus: orderType.value, isClear: false }
) => {
  loading.value = true;
  uni.showLoading({
    title: `加载中...`,
    mask: true,
  });
  return new Promise((resolve, reject) => {
    // getOrder({
    //   pageVO,
    //   data: {
    //     allocationStatus:
    //       params.orderStatus != DistributeOrderStatusParams.all
    //         ? params.orderStatus
    //         : "" ,
    //   },
    // })
    //   .then((res) => {
    //     if (params.isClear) orderListRef.value = [];
    //     orderListRef.value.push(...res.records);
    //     total.value = res.total;
    //   })
    //   .catch((err) => {
    //     if (params.isClear) orderListRef.value = [];
    //     uni.showToast({
    //       title: `获取订单列表失败${err}`,
    //       icon: "none",
    //     });
    //   })
    //   .finally(() => {
    //     resolve(true);
    //     wx.hideLoading({ noConflict: true });
    //     loading.value = false;
    //   });
  });
};
</script>

<style lang="scss">
@import "@/static/css/fonts.scss";
page {
  background-color: #f3f3f3;
}
.balance {
  padding: 20rpx;
  box-sizing: border-box;
  .banlance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 190rpx;
    padding: 0rpx 68rpx;
    box-sizing: border-box;
    box-shadow: 0px 7px 16px 0px rgba(129, 182, 255, 0.694);
    border-radius: 24rpx;
    .banlance-header-number {
      color: #ffffff;
      .text {
        font-size: 24rpx;
      }
      .number {
        font-size: 64rpx;
        font-weight: bold;
        font-family: "DINPro";
      }
    }
    .banlance-header-btn {
      .btn {
        width: 120rpx;
        height: 60rpx;
        font-size: 24rpx;
        color: #fff !important;
      }
    }
  }
  .banlance-content {
    margin-top: 24rpx;
    border-radius: 16rpx;
    overflow: hidden;
    background: #ffffff;
    .emptyBox {
      height: 700rpx;
      text-align: center;
      padding-top: 50rpx;
      box-sizing: border-box;
      .emptyImg {
        width: 400rpx;
        height: 400rpx;
      }
    }
  }
}
</style>
