<template>
  <view class="controls-container">
    <view class="time">{{time}}</view>
    <view class="controls-group" >
      <view class="control-item" @click="handleMute" >
        <image :src="mediaState.isMute ? muteIcon : unMuteIcon" mode="scaleToFill" class="item-img" />
        <view class="item-text">静音</view>
      </view>
      <view class="control-item" @click="handleHangUp">
        <image :src="hangUpIcon" mode="scaleToFill" class="item-img" />
        <view class="item-text">挂断</view>
      </view>
      <view class="control-item" @click="handleSwitchCamera">
        <image :src="mediaState.frontOrBack === 'front' ? switchCameraIcon : unSwitchCameraIcon" mode="scaleToFill" class="item-img" />
        <view class="item-text">切换摄像头</view>
      </view>
      <view class="control-item" @click="handleSpeaker">
        <image :src="mediaState.isSpeakerOn ? unSpeaker : speaker" mode="scaleToFill" class="item-img" />
        <view class="item-text">扬声器</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import hangUpIcon from "@/static/images/inquiry/videoChatIcon/hangUpIcon.png";
import muteIcon from "@/static/images/inquiry/videoChatIcon/muteIcon.png";
import speaker from "@/static/images/inquiry/videoChatIcon/speaker.png";
import switchCameraIcon from "@/static/images/inquiry/videoChatIcon/switchCameraIcon.png";
import unMuteIcon from "@/static/images/inquiry/videoChatIcon/unMuteIcon.png";
import unSpeaker from "@/static/images/inquiry/videoChatIcon/unSpeaker.png";
import unSwitchCameraIcon from "@/static/images/inquiry/videoChatIcon/unSwitchCameraIcon.png";

import useVideoChat from '@/subPackages/Live/hooks/useVideoChat.js';
import { onLoad } from "@dcloudio/uni-app";
const { mediaState, handleMute, handleSpeaker, handleSwitchCamera, handleHangUp } = useVideoChat();

// 计时器相关变量
const time = ref('00:00');
const startTime = ref<number>(0);
const timer = ref<number | null>(null);

/** 格式化时间显示 */
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // 大于1小时显示 小时:分钟:秒，否则只显示 分钟:秒
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
};

/** 开始计时 */
const startTimer = () => {
  startTime.value = Date.now();
  time.value = '00:00';

  timer.value = setInterval(() => {
    const elapsed = Math.floor((Date.now() - startTime.value) / 1000);
    time.value = formatTime(elapsed);
  }, 1000);
};

/** 停止计时 */
const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

/** 组件挂载时开始计时 */
onMounted(() => {
  // 如果还没有开始计时，则开始计时
  if (!timer.value) {
    startTimer();
  }
});

/** 组件卸载时停止计时 */
onUnmounted(() => {
  stopTimer();
});

</script>
<style scoped lang="less">
.controls-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 400rpx;
  padding: 48rpx 24rpx;
  box-sizing: border-box;
  .time{
    color: #FFFFFF;
    text-align: center;
    padding: 24rpx 0;
    box-sizing: border-box;
  }
  .controls {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .controls-group {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    width: 100%;
    .control-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      gap: 12rpx;

      .item-img {
        width: 128rpx;
        height: 128rpx;
      }
      .item-text{
        font-size: 24rpx;
        color: #FFFFFF;
      }
    }
  }
}
</style>
