<template>
  <view class="balance-card">
    <view class="balance-card-top">
      <view class="balance-card-top-left">
        成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除成本扣除
      </view>
      <view class="balance-card-top-right"> ￥100.00 </view>
    </view>
    <view class="balance-card-bottom">
      <view class="balance-card-bottom-left">
        <view class="balance-card-bottom-left-title">关联单号：</view>
        <view class="balance-card-bottom-left-code">1234567890</view>
      </view>
      <view class="balance-card-bottom-right">
        <view class="balance-card-bottom-right-time">2023-01-01 12:00:00</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from "vue";
</script>
<style scoped lang="scss">
.balance-card {
  padding: 24rpx;
  border-bottom: 1px solid #eeeeee;
  .balance-card-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    color: #333333;
    gap: 40rpx;
    font-size: 28rpx;
    .balance-card-top-left {
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }

    .balance-card-top-right {
      color: #333333;
    }
  }

  .balance-card-bottom {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    .balance-card-bottom-left {
      flex: 1;
      display: flex;
      .balance-card-bottom-left-title {
        color: #666666;
        margin-bottom: 8rpx;
      }

      .balance-card-bottom-left-code {
        color: #333333;
        font-weight: 500;
      }
    }

    .balance-card-bottom-right {
      .balance-card-bottom-right-time {
        color: #999999;
        text-align: right;
      }
    }
  }
}
</style>
