<template>
  <view class="chinese-medicine-card">
    <view class="card-header">
      <view class="medicine-title">中药方剂</view>
      <view class="price">
        <text class="currency">￥</text>
        <text class="amount">{{ (medicineInfo?.price / 100).toFixed(2) || '00.00' }}</text>
      </view>
    </view>

    <view class="card-content">
        <text class="label">剂数</text>
        <text class="value">×{{ medicineInfo?.dosage || '0' }}</text>
    </view>

    <view class="card-footer"  @click="handleDetail" v-if="isShowDetail" >
        <view class="detail-text">处方详情</view>
        <image class="arrow" :src="rightArrow" mode="scaleToFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import rightArrow from "@/static/images/user/rightArrow.png";
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";

// 定义props
interface MedicineInfo {
  price?: number;
  dosage?: string | number;
  prescriptionId?: string;
}

const props = withDefaults(defineProps<{
  medicineInfo?: MedicineInfo;
  isShowDetail?: boolean;
}>(), {
  medicineInfo: () => ({}),
  isShowDetail: true,
})

// 显示处方详情
const handleDetail = () => {
  navigateTo({
    url: RouteName.PrescriptionFormDetail,
    props: {
      id: props.medicineInfo.prescriptionId
    }
  })
}
</script>
<style scoped lang="scss">
.chinese-medicine-card {
  border-radius: 16rpx;
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .medicine-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .price {
      display: flex;
      align-items: baseline;

      .currency {
        font-size: 24rpx;
        color: #333333;
        margin-right: 4rpx;
      }

      .amount {
        font-size: 36rpx;
        font-weight: 600;
        color: #333333;
      }
    }
  }

  .card-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        font-size: 28rpx;
        color: #666666;
        margin-right: 16rpx;
      }

      .value {
        font-size: 28rpx;
        color: #999999;
        font-weight: 500;
      }
  }

  .card-footer {
    
    margin-top: 32rpx;
    background: #F8F8F8;
    border-radius: 8rpx;
    height: 80rpx;
    padding: 20rpx;
    box-sizing: border-box;
    
    display: flex;
    justify-content: space-between;
    align-items: center;
    .detail-text {
      font-size: 28rpx;
      color: #333333;
    }

    .arrow {
      width: 28rpx;
      height: 28rpx;
    }
  }
}
</style>