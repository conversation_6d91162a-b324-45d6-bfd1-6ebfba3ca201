<template>
  <van-popup
    :show="show"
    @close="onClose"
    root-portal
    position="bottom"
    round
    closeable
  close-icon="close"
  >
    <view class="doctor-service-modal">
      <view class="doctor-service-modal-title">
        <text>服务类型</text>
      </view>
      <view class="doctor-service-modal-content">
        <doctorServices :doctorDetailData="doctorDetailData" />
      </view>
    </view>
  </van-popup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { type DoctorDetail } from "../type";
import doctorServices from "./doctorServices.vue";
import { onShow } from "@dcloudio/uni-app";
const props = defineProps<{
  show: boolean;
  doctorDetailData: DoctorDetail;
}>();

onShow(() => {
  show.value = false;
});

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
}>();

const show = computed({
  get() {
    return props.show;
  },
  set(value) {
    emit("update:show", value);
  },
});

const onClose = () => {
  emit("update:show", false);
};

</script>

<style lang="scss" scoped>
@import "../css/doctorStyle.scss";
.doctor-service-modal {
  background-color: #fff;
  padding: 32rpx 16rpx;
  border-radius: 32rpx;
  .doctor-service-modal-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }
}
</style>
