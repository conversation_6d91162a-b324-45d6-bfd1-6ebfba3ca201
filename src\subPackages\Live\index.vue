<template>
  <view class="live-container">
    <!-- <view class="noticeBar" :style="CustomStyle" v-if="endConfig.isShow && !endConfig.isClose">
      <view class="noticeContainer" >
        <view class="noticeText" >{{formatResidueTime}}后自动结束视频</view>
        <van-icon name="cross" size="34rpx" @click="closeNotice" />
      </view>
    </view> -->
    <!-- 播放器 -->
    <videoPlayWarpper/>
    <!-- 操作 -->
    <controls-group />
  </view>
</template>
<script setup lang="ts">
import { computed, reactive, ref, onMounted, onUnmounted } from "vue";
import controlsGroup from "./components/controls-group/index.vue";
import videoPlayWarpper from "./components/videoPlayWarpper/index.vue";
import { onLoad, onUnload } from '@dcloudio/uni-app'
import useVideoChat from '@/subPackages/Live/hooks/useVideoChat.js';
import { navigateBack } from "@/routes/utils/navigateUtils";
import { useRectInfo } from "@/hooks";
const { getRectSizeInfo, rectInfo } = useRectInfo();
import { InquiryStatusEnum } from "@/enum/inquiryEnum";
import { getVolcanoToken, getVolcanoTokenForPhysician } from '@/services/api/inquiry';
import dayjs from "dayjs";
const { init, handleHangUp } = useVideoChat();
/** 问诊单id */
const inquiryId = ref<string>('');
/** 是否医助 */
const isPhysician = ref<boolean>(false);
/** 问诊单结束时间 */
const endConfig = reactive({
  /** 结束时间 */
  endTime: 0,
  /** 剩余时间 */
  residueTime: 0,
  /** 是否提示 */
  isShow: false,
  /** 是否已关闭提示 */
  isClose: false,
  /** 倒计时 */
  timer: null
})

onLoad((options) => {
  inquiryId.value = options.inquiryId || '';
  isPhysician.value = options.isPhysician == '1';
  getRectSizeInfo()
  getVolcanoTokenFn();
});

onUnload(() => {
  handleHangUp(false);
});



/** 结束视频通话 */
const endVideoCall = () => {
  uni.showToast({
    title: '视频通话已结束',
    icon: 'none'
  });
  
  setTimeout(() => {
    navigateBack();
  }, 1500);
};

/** 获取视频信息 火山token */
const getVolcanoTokenFn = async()=>{
  uni.showLoading({
    title: '加载中...'
  });
  try {
    const getVolcanoTokenApi= isPhysician.value ? getVolcanoTokenForPhysician : getVolcanoToken;
    const res = await getVolcanoTokenApi(inquiryId.value)
    if (res.consultationStatus != InquiryStatusEnum.CONSULTING && res.consultationStatus != InquiryStatusEnum.WAITING) {
      showError('当前问诊单状态不正确，请检查');
      return;
    }
    await init({
      uid: res.userId,
      roomId: res.roomId,
      token: res.token,
      appid: res.appId?.toString()
    })
    // init({
    //   uid: 'user3',
    //   roomId: 'test1234',
    //   token: '001683a5e93cd8a350179707168QwCtH00FD+9LaI8pVWgIAHRlc3QxMjM0BQB1c2VyMwYAAACPKVVoAQCPKVVoAgCPKVVoAwCPKVVoBACPKVVoBQCPKVVoIAAhBHRfltEomPQdj8kUqV31n+27GZDj/fm5EjozSH5exg==',
    //   appid: '683a5e93cd8a350179707168'
    // })
    uni.hideLoading();
  } catch (error) {
    console.log('获取火山token失败', error);
    showError(`${error}`);
  }
  
  function showError(err:string) {
    uni.hideLoading();
    uni.showToast({
      title: err,
      icon: 'none'
    });
    setTimeout(() => {
      navigateBack()
    }, 1500);
  }
}


</script>
<style lang="scss" scoped>
.live-container {
  height: 100vh;
  width: 100vw;
  background: #272323;
  .noticeBar{
    position: fixed;
    top: 100rpx;
    background: transparent;
    padding: 0rpx 40rpx;
    box-sizing: border-box;
    width: 100vw;
    z-index: 999;
    .noticeContainer{
      display: flex;
      padding: 24rpx;
      align-items: center;
      justify-content: space-between;
      background-color: #FFFBE8;
      color: #ED6A27;
      border-radius: 12rpx;
    }
  }
}
</style>
