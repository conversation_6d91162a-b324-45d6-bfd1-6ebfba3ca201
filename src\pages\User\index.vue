<template>
  <view class="user" :style="{ backgroundImage: `url(${userHomeBg})`}">
    <view class="header" :style="customStyle" @click="loginFn">
      <view class="haeder-avatar">
        <!-- <image src="https://img.yzcdn.cn/vant/cat.jpeg" mode="scaleToFill" /> -->
        <van-image
          round
          width="120rpx"
          height="120rpx"
          :src="userInfo.img ? userInfo.img : defaultAvatar"
        >
          <van-loading slot="loading" type="spinner" size="20" vertical />
        </van-image>
      </view>
      <view class="header-info">
        <view class="info-name">{{
          userInfo.nickname ? userInfo.nickname : "未设置昵称"
        }}</view>
        <!-- <view class="info-id">{{ userInfo.id }}</view> -->
      </view>
      <view class="header-contact">
        <button open-type="contact">
          <image :src="contact" mode="scaleToFill" /> 客服
        </button>
      </view>
    </view>
    <view class="userLevel">
      <userLevel :presentLevel="presentLevel" v-if="isUseLevel" />
    </view>
    <view class="container">
      <!-- 我的订单 -->
      <view class="myOrder">
        <view class="order mt2">
          <view class="order-top">
            <view class="order-title">我的订单</view>
            <view
              class="order-all"
              @click="jumpTo(RouteName.Order, { active: 0 })"
              >全部</view
            >
            <view class="right-icon"><van-icon name="arrow" /></view>
          </view>
          <recipeHint/>
          <view class="order-content">
            <template
              v-for="(item, index) in orderItems"
              :key="index"
            >
              <view
                class="order-item"
                @click="jumpTo(item.url, item.props)"
                v-if="item.isShow"
              >
                <view class="item-img">
                  <image :src="item.img" mode="scaleToFill" />
                  <view class="van-badge" v-if="item.count != 0">{{
                    item.count
                  }}</view>
                </view>
                <view class="item-info">{{ item.name }}</view>
              </view>
            </template>
          </view>
        </view>
      </view>
      <!-- 我的服务 -->
      <view class="myService">
        <view class="service-top">
          <view class="service-title">我的服务</view>
        </view>
        <view class="service-content">
          <template v-for="(item, index) in serviceItems" :key="index">
            <view
              class="service-item"
              v-if="item.isShow"
              @click="jumpTo(item.url, item.props)"
            >
              <view class="item-icon">
                <image :src="item.img" mode="scaleToFill" />
                <view class="van-badge" v-if="item.count">{{
                  item.count
                }}</view>
              </view>
              <view class="item-info">{{ item.name }}</view>
            </view>
          </template>
        </view>
      </view>
    </view>
    <Tabbar />
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import userRectInfo from "@/hooks/useRectInfo";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { type ItemInfo, PreType } from "./types";
import {
  getUserInfo,
  getPres,
  getCountMyOrders,
  getCountPrescribed,
} from "@/services/api/user";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";
import userHomeBg from "@/static/images/user/userHomeBg.png";
import { navigateTo, switchTab } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { UserType, IsDistributor, isBindPhysType } from "@/enum/userTypeEnum";
import { storeToRefs } from "pinia";
import { watch } from "vue";
import { systemStore } from "@/stores/modules/system";
// import { order } from "../Order/hooks";
import userLevel from "./components/userLevel.vue";
import { useLevel } from "@/pages/IntegralHome/hooks";
import { routesMap } from "@/routes/maps";
import Tabbar from "@/components/Tabbar/index.vue";
import recipeHint from "./components/recipeHint.vue";
import { useTabbar } from "@/components/Tabbar/hooks/useTabbar";
import contact from "@/static/images/user/contact.png";
import { StoreTypeEnum } from "@/enum/storeNamesEnum";

const { setSelectedTabKey, setTabbarDisplay } = useTabbar();
const { presentLevel, isUseLevel } = useLevel();
const userStore = useUserInfoStoreWithoutSetup();
const { info: _userInfo, token } = storeToRefs(userStore);
const { getStoreType, isUsingDistribution, storeType, isAfterSaleEnabled } = storeToRefs(
  systemStore()
);
const { getRectSizeInfo, rectInfo } = userRectInfo();
const userInfo = reactive({
  nickname: "",
  id: "",
  img: "",
});

onLoad(async () => {
  //获取头部信息
  getRectSizeInfo();
});

onShow(() => {
  if (userStore.token) {
    getUserInfoFn();
    getOrderCount();
  } else {
    userInfo.nickname = "点此登录";
  }
  setSelectedTabKey(routesMap[RouteName.User].path);
  setTabbarDisplay(true);
  // const pageInstance = getCurrentPages()[0];
  // pageInstance.getTabBar((tabBar)=>{
  //     tabBar.setData({
  //         selected:routesMap[RouteName.User].path
  //     })
  // })
});
// 获取个人信息
const getUserInfoFn = () => {
  getUserInfo()
    .then((res) => {
      userInfo.nickname = res.nickname;
      userInfo.id = res.id;
      userInfo.img = res.img;
      userStore.setUserInfo(res);
    })
    .catch((err) => {
      const errText = err.data?.message || "获取个人信息失败";
      uni.showToast({
        title: errText,
        icon: "none",
      });
    });
};

// 距离顶部距离
const customStyle = computed(() => {
  return {
    marginTop: `${rectInfo.value.mTSize + rectInfo.value.rectbtnHeight}px`,
  };
});


const orderCount = reactive({
  pendingPaymentCount: 0,
  toBeShippedCount: 0,
  toBeReceivedCount: 0,
  afterSale: 0,
});
// 获取我的订单数量
const getOrderCount = () => {
  getCountMyOrders()
    .then((res) => {
      Object.assign(orderCount, res);
    })
    .catch((err) => {
      const errText = err.data?.message || "获取订单数量失败";
      uni.showToast({
        title: errText,
        icon: "none",
      });
    });
};

const orderItems = computed((): ItemInfo[] => [
  {
    img: "../../static/images/user/waitPay.png",
    name: "待付款",
    url: RouteName.Order,
    props: { active: 1 },
    count: orderCount.pendingPaymentCount,
    isShow: true,
  },
  {
    img: "../../static/images/user/waitConsign.png",
    name: "待发货",
    url: RouteName.Order,
    props: { active: 2 },
    count: orderCount.toBeShippedCount,
    isShow: true,
  },
  {
    img: "../../static/images/user/waitGather.png",
    name: "待收货",
    url: RouteName.Order,
    props: { active: 3 },
    count: orderCount.toBeReceivedCount,
    isShow: true,
  },
  {
    img: "../../static/images/user/afterSale.png",
    name: "退款/售后",
    url: RouteName.Order,
    props: { active: 4 },
    count: orderCount.afterSale,
    isShow: isAfterSaleEnabled.value,
  },
]);

const serviceItems = computed(() => [
  // {
  //   img: "../../static/images/user/myRecipe.png",
  //   name: "我的处方",
  //   url: RouteName.PrescriptionList,
  //   count: 0,
  //   isShow: getStoreType.value,
  // },
  {
    img: "../../static/images/user/medicalConsultation/medicalConsultationForm.png",
    name: "问诊单",
    url: RouteName.ListOfMedicalConsultations,
    count: 0,
    isShow: storeType.value === StoreTypeEnum.medicine,
  },
  {
    img: "../../static/images/user/myRecipe.png",
    name: "处方单",
    url: RouteName.ListOfPrescriptions,
    count: 0,
    isShow: true,
  },
  {
    img: "../../static/images/user/MyDoctor.png",
    name: "我的医生",
    url: RouteName.MyDoctorList,
    count: 0,
    isShow: storeType.value === StoreTypeEnum.medicine,
  },
  {
    img: "../../static/images/user/MyAppointment.png",
    name: "我的预约",
    url: RouteName.MyAppointment,
    count: 0,
    isShow: storeType.value === StoreTypeEnum.medicine,
  },
  {
    img: "../../static/images/user/drugTaker.png",
    name: "用药人",
    url: RouteName.UserBuyerList,
    count: 0,
    isShow: getStoreType.value,
  },
  {
    img: "../../static/images/user/shippingAddress.png",
    name: "收货地址",
    url: RouteName.UserAddress,
    count: 0,
    isShow: true,
  },
  {
    img: "../../static/images/user/coupon.png",
    name: "优惠券",
    url: "toast",
    count: 0,
    isShow: false, // 于1.1.3版本隐藏优惠券
  },
  {
    img: "../../static/images/user/points.png",
    name: "积分",
    url: RouteName.IntegralHome,
    count: 0,
    isShow: true,
  },
  {
    img: "../../static/images/user/distribution.png",
    name: "分销中心",
    url: RouteName.Distribute,
    count: 0,
    isShow:
      _userInfo.value.isDistributor == IsDistributor.Yes &&
      isUsingDistribution.value,
  },
  {
    img: "../../static/images/user/AppointmentForMedicalConsultation.png",
    name: "医助后台",
    url: RouteName.AppointmentForMedicalConsultation,
    count: 0,
    isShow: _userInfo.value.isBindPhys == isBindPhysType.BOUND,
  },
  {
    img: "../../static/images/user/balanceIcon.png",
    name: "账户余额",
    url: RouteName.Balance,
    count: 0,
    isShow: false,
  },
  {
    img: "../../static/images/user/setting.png",
    name: "设置",
    url: RouteName.UserSettings,
    count: 0,
    isShow: true,
  },
  {
    img: "../../static/images/user/behalfOrder.png",
    name: "代下单",
    url: RouteName.OrderAgent,
    count: 0,
    isShow: _userInfo.value.type == UserType.GroupMgr,
  },
]);

const jumpTo = (
  url: RouteName | "toast",
  props: Record<string, string | number>
): void => {
  if (url !== "toast") {
    if (url == RouteName.IntegralHome) {
      switchTab({ url });
      return;
    }
    // uni.navigateTo({
    //   url: '/subPackages/User/address/index',
    // });
    navigateTo({ url, props });
  } else {
    uni.showToast({
      title: "开发中...",
      icon: "none",
    });
  }
};

// 登录
const loginFn = () => {
  if (!userStore.token) {
    navigateTo({
      url: RouteName.Login,
    });
  }
};
watch(
  () => _userInfo.value.type,
  () => {
    serviceItems.value.forEach((item) => {
      if (item.name == "代下单") {
        item.isShow = _userInfo.value.type == UserType.GroupMgr;
      }
    });
  }
);

watch(
  () => getStoreType.value,
  (val) => {
    serviceItems.value.forEach((item) => {
      if (item.name == "我的处方" || item.name == "用药人") {
        item.isShow = val;
      }
    });
  }
);

watch(
  () => token.value,
  (newV) => {
    const targer = serviceItems.value.find((item) => item.name === "积分");
    targer.url = newV ? RouteName.IntegralHome : RouteName.Login;
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
@import "@/components/Tabbar/style.scss";
.user {
  height: calc(100vh - $tabber-height);
  overflow-y: auto;
  // padding: 24rpx;
  box-sizing: border-box;

  background-size: 100% 50%;
  background-repeat: no-repeat;
  .header {
    padding: 24rpx;
    display: flex;
    align-items: center;
    .haeder-avatar {
      width: 120rpx !important;
      height: 120rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 40rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .header-info {
      width: calc(100% - 120rpx - 40rpx - 120rpx) !important;

      .info-name {
        font-size: 32rpx;
        font-weight: bold;
      }
    }
    .header-contact {
      width: 120rpx;
      position: relative;
      right: -24rpx;

      :deep(button) {
        height: 100rpx;
        line-height: 50rpx;
        outline: none;
        background: transparent;
        margin: 0;
        padding: 0;
        padding-left: 14rpx;
        box-sizing: border-box;
        font-size: 24rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #666666;
        border-radius: 48rpx 0rpx 0rpx 48rpx;
        &::after {
          border: none;
        }
      }
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
  .userLevel {
    padding: 0rpx 32rpx;
  }
  .container {
    padding: 24rpx;
    box-sizing: border-box;
    background: linear-gradient(180deg, #ffffff 0%, #f8f8f8 30%, #f8f8f8 100%);
    border-radius: 24rpx;
  }

  .myOrder {
    

    .order {
      background: #fff;
      border-radius: 20rpx;

      &.mt5 {
        margin-top: -50rpx;
      }

      &.mt2 {
        margin-top: 20rpx;
      }

      .order-top {
        display: grid;
        grid-template-columns: 9fr 1fr 0.5fr;
        padding: 20rpx 20rpx;
        border-bottom: 1px solid #f5f5f5;

        .order-title {
          font-size: 28rpx;
          font-weight: bold;
        }

        .order-all {
          font-size: 28rpx;
          color: #999;
        }

        .right-icon {
          font-size: 28rpx;
          color: #999;
        }
      }

      .order-content {
        margin-top: 20rpx;
        padding: 20rpx;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 20rpx;
        display: flex;
        justify-content: space-around;

        .order-item {
          width: 116rpx;
          display: flex;
          flex-direction: column;
          align-items: center;

          .item-img {
            width: 60rpx;
            height: 60rpx;

            image {
              width: 100%;
              height: 100%;
            }
          }

          .item-info {
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .myService {
    margin-top: 16rpx;
    background: #fff;
    border-radius: 20rpx;

    .service-top {
      padding: 20rpx;
      border-bottom: 1px solid #f5f5f5;

      .service-title {
        font-size: 28rpx;
        font-weight: bold;
      }
    }

    .service-content {
      // grid布局行高132rpx 每行固定3个
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      padding: 20rpx;
      box-sizing: border-box;

      .service-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 132rpx;

        .item-icon {
          width: 64rpx;
          height: 64rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .item-info {
          font-size: 24rpx;
          margin-top: 10rpx;
        }
      }
    }
  }

  // 徽标
  .myService,
  .myOrder {
    .item-img,
    .item-icon {
      position: relative;

      .van-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        min-width: 28rpx;
        height: 28rpx;
        background: red;
        color: #ffffff;
        font-size: 20rpx;
        text-align: center;
        line-height: 30rpx;
        border-radius: 50%;
      }
    }
  }
}
</style>
<style lang="scss">
page {
  height: 100%;
  // 背景渐变
  background: #f8f8f8;
}
</style>
