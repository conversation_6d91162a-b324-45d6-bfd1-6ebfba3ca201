import { JRequest } from "@/services/index";

const enum InquiryApiEnum {
  /** 科室列表 */
  departmentList = "/applet/departmentConfig/list",
  /** 按科室id搜索医生 */
  doctorListById = "/applet/doctorEntity/pageByDepartId",
  /** 搜索医生 */
  doctorSearch = "/applet/doctorEntity/pageByKey",
  /** 医生推荐 */
  doctorRecommend = "/applet/doctorEntity/getCommandDoctor",
  /** 科室推荐 */
  departmentRecommend = "/applet/departmentConfig/listCommandDeparts",
  /** 医生详情 */
  doctorDetail = "/applet/doctorEntity/get",
  /** 去咨询 */
  getJumpFlagForCounseling = "/applet/pres/getJumpFlagForCounseling",
  /** 创建问诊单 */
  createPrescription = "/applet/pres/create",
  /** 待接诊问诊卡信息 */
  getPresCardInfo = "/applet/pres/getPresCardInfo",
  /** 图文待接诊轮询状态 */
  getPresStatus = "/applet/imUser/getConversationInfo",
  /** 获取单个联系人记录 */
  getContact = "/applet/imContact/getContact",
  /** 获取火山token（患者） */
  getVolcanoToken = "/applet/hs/rtc/getRtcToken/cs",
  /** 获取火山token（医生助理） */
  getVolcanoTokenForPhysician = "/applet/hs/rtc/getRtcToken/physician",
  /** 视频待接诊轮询接口 */
  getVideoPresStatus = "/applet/pres/getVideoPresInfo",
}

/** 科室列表 */
export async function getDepartmentList() {
  return JRequest.get({ url: InquiryApiEnum.departmentList });
}

/** 按科室id搜索医生 */
export async function doctorListById(params) {
  return JRequest.post({
    url: InquiryApiEnum.doctorListById,
    params,
  });
}

/** 搜索医生 */
export async function doctorSearch(params) {
  return JRequest.post({
    url: InquiryApiEnum.doctorSearch,
    params,
  });
}

/** 医生推荐 */
export async function doctorRecommend() {
  return JRequest.get({
    url: InquiryApiEnum.doctorRecommend,
  });
}

/** 科室推荐 */
export async function departmentRecommend() {
  return JRequest.get({
    url: InquiryApiEnum.departmentRecommend,
  });
}

export interface DoctorDetail {
  /**
   * 医生擅长
   */
  beGoodAt: string;
  /**
   * 问诊费用，单位分/次。0代表免费
   */
  consultationFee: number;
  /**
   * 科室名称
   */
  departmentName: string;
  /**
   * 医师资格证书编码：后端已掩码返回
   */
  doctorCode?: string;
  /**
   * 医师姓名
   */
  doctorName: string;
  /**
   * 后台配置的问诊时长。单位分钟
   */
  duration: number;
  /**
   * 医生ID
   */
  id: number | string;
  /**
   * 头像
   */
  img: string;
  /**
   * 机构名称
   */
  institutionName: string;
  /**
   * 医生简介
   */
  introduction: string;
  /**
   * 是否支持图文问诊。0=关闭；1=开启
   */
  isPictureText: number;
  /**
   * 在线状态。0=否；1=是
   */
  onlineStatus: number;
  /**
   * 职称。1=主任医师；2=副主任医师；3=主治医师；4=住院医师；5=医士
   */
  title: number;
  /**
   * 是否支持视频问诊。0=关闭；1=开启
   */
  isVideo: number;
  /**
   * 视频问诊费用，单位分/次。0代表免费
   */
  videoConsultationFee: number;
  /** 图文问诊时长 */
  pictureDuration: number;
  /** 视频问诊时长 */
  videoDuration: number;
}
/** 医生详情 */
export async function doctorDetail(id: string): Promise<DoctorDetail> {
  return JRequest.get({
    url: `${InquiryApiEnum.doctorDetail}?id=${id}`,
  });
}

/** 获取咨询状态 */
export async function getJumpFlagForCounseling(doctorId: string | number,type:1|2) {
  return JRequest.get({
    url: `${InquiryApiEnum.getJumpFlagForCounseling}?doctorId=${doctorId}&form=${type}`,
  });
}

/** 创建问诊单 */
export async function createPrescription(params) {
  return JRequest.post({
    url: InquiryApiEnum.createPrescription,
    params,
  });
}

/** 待接诊问诊卡信息 */
export async function getPresCardInfo(inquiryId: string) {
  return JRequest.post({
    url: `${InquiryApiEnum.getPresCardInfo}?id=${inquiryId}`,
  });
}

/** 图文待接诊轮询问诊单状态 */
export async function getPresStatus(doctorId: string) {
  return JRequest.get({
    url: `${InquiryApiEnum.getPresStatus}?doctorId=${doctorId}`,
  });
}

/** 获取单个联系人记录 */
export async function getContact(params) {
  return JRequest.get({
    url: InquiryApiEnum.getContact,
    params,
  });
}

/** 获取火山相关信息 token 结束时间（患者） */
export async function getVolcanoToken(inquiryId: string) {
  return JRequest.get({
    url: `${InquiryApiEnum.getVolcanoToken}?presId=${inquiryId}`,
  });
}

/** 获取火山相关信息 token 结束时间（医生助理） */
export async function getVolcanoTokenForPhysician(inquiryId: string) {
  return JRequest.get({
    url: `${InquiryApiEnum.getVolcanoTokenForPhysician}?presId=${inquiryId}`,
  });
}

/** 视频待接诊轮询接口 */
export async function getVideoPresStatus(inquiryId: string) {
  return JRequest.get({
    url: `${InquiryApiEnum.getVideoPresStatus}?id=${inquiryId}`,
  });
}
