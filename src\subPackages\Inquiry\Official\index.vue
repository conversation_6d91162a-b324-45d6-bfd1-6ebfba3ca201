<template>
  <view class="official-page" >
    <image
        class="official-page-img"
        :src="helpImg"
        mode="scaleToFill"
    />

    <view class="official-page-footer" :style="customStyle" >
        <view
            v-for="item in tabs"
            :key="item.name"
            @click="jumpTo(item)"
            class="tab"
        >{{ item.name }}</view>
    </view>
  </view>

</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed } from 'vue'
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo, switchTab } from "@/routes/utils/navigateUtils";
import helpImg from "@/static/images/inquiry/25126/modalLogo.png"
import userRectInfo from "@/hooks/useRectInfo";
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import { navigationMap } from "@/components/Tabbar/hooks/useTabbar";
import {routesMap} from "@/routes/maps";

onShareAppMessage(()=>{
  return {
    title: '帮你找名医',
    path: `/subPackages/Inquiry/Official/index`,
    imageUrl:helpImg
  }
})

const { getRectSizeInfo, rectInfo } = userRectInfo();
// 距离底部距离
const customStyle = computed(() => {
  return {
    paddingBottom: `${rectInfo.value.safeAreaBottom}rpx`,
  };
});

onLoad(()=>{
    getRectSizeInfo()
})

const tabs = ref([
    {
        name:'养生',
        targetRoute:RouteName.InquiryDoctorList,
        isOpen:false
    },
    {
      name:'我的',
      targetRoute:RouteName.User,
      isOpen:true  
    },
    {
        name:'视频看诊',
        targetRoute:RouteName.MyAppointment,
        isOpen:true
    }
])

const jumpTo = (item: any) => {
  if (!item.isOpen) {
    uni.showToast({
      title: "暂未开放，敬请期待",
      icon: "none",
    });
    return;
  }
  const isTabbarRoute = Object.values(navigationMap).some(
    (route) => route.pagePath === routesMap[item.targetRoute].path
  );
  const jumpFn = isTabbarRoute ? switchTab : navigateTo;
  jumpFn({
    url: item.targetRoute
  });
  
};

</script>
<style scoped lang="scss">
.official-page{
    text-align: center;
    .official-page-img{
        width: 460rpx;
        height: 360rpx;
        margin-top: 140rpx;
    }
    .official-page-footer{
        position: fixed;
        width: 100%;
        bottom: 0rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        background-color: #fff;
        padding-top: 6rpx;
        .tab{
            flex: 1;
            font-size: 28rpx;
            color: #333333;
            font-weight: 400;
            border-right: 1rpx solid #DCDCDC;
            height: 80rpx;
            line-height: 80rpx;
            &:last-child{
                border: none;
            }
        }
    }
}
</style>
<style>
page{
    background-color: #EDEDED;
}
</style>