<template>
  <view class="appointment-card">
    <view class="appointment-detail">
      <view class="appointment-detail-left">
        <view class="appointment-detail-left-t">
          <text class="appointment-detail-name">{{ props.info.doctorName }}</text>
          <text class="appointment-detail-title">{{ doctorTitleMap[props.info.title] }}</text>
          <text class="appointment-detail-departmentName">{{ props.info.departmentName }}</text>
        </view>
        <view class="appointment-detail-left-b">
          <text class="appointment-detail-institutionName">{{ props.info.institutionName }}</text>
        </view>
      </view>
      <view class="appointment-detail-right">
        <van-image style="margin: 0; padding: 0;height: auto;" fit="fill" width="112rpx" height="112rpx" :src='props.info.doctorImg'/>
      </view>
    </view>
    <view class="appointment-message">
      <view class="appointment-detail-col">
        <text class="appointment-detail-col-l">问诊时间:</text>
        <text class="appointment-detail-col-r">{{ props.info.preBookTime }}</text>
      </view>
      <view class="appointment-detail-col">
        <text class="appointment-detail-col-l">用户昵称：</text>
        <text class="appointment-detail-col-r">{{ props.info.nickname }}</text>
      </view>
      <view class="appointment-detail-col appointment-detail-col-conclusion">
        <text class="appointment-detail-col-l">患者姓名：</text>
        <text class="appointment-detail-col-r">{{ props.info.patientName }}</text>
      </view>
      <view class="appointment-message-static"
      :style="{
        backgroundColor:static_list[props.info.consultationStatus].color
      }">{{ static_list[props.info.consultationStatus].name }}</view>
    </view>
    <view v-if="props.info.consultationStatus === medicalConsultationStatus.PendingConsultation || props.info.consultationStatus === medicalConsultationStatus.InConsultation" class="appointment-btn">
      <van-button custom-style="background: #1677FF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        float: right;
        width: calc(100vw - 48px);"
        @click="toastShow"
      >进入视频</van-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import medicalDoctor from "@/static/images/user/medicalConsultation/medicalDoctor.png"
import type {myAppointment_list} from "@/subPackages/User/myAppointmentList/hooks/type";
import {useCommon} from "@/hooks";
import myAppointment from "@/subPackages/User/myAppointmentList/hooks";
import {getVideoPresStatus} from "@/services/api/inquiry";
import {InquiryStatusEnum} from "@/enum/inquiryEnum";
import {medicalConsultationStatus} from "@/enum/medicalConsultationEnum";
const { jumpToUrl } = useCommon()
const {
  static_list,
  isTimeDifferenceLessThanTenMinutes
} = myAppointment()
interface Props {
  info : myAppointment_list;
}
const props = withDefaults(defineProps<Props>(), {
  info: () => ({} as myAppointment_list),
})
const doctorTitleMap = ["主任医师" ,"副主任医师" ,"主治医师" ,"住院医师" ,"医士"]
const toastShow = async () => {
  if (props.info.consultationStatus !== medicalConsultationStatus.InConsultation &&
      props.info.preBookTime && isTimeDifferenceLessThanTenMinutes(props.info.preBookTime)) {
    await uni.showToast({
      title: "请在问诊时间前10分钟进入候诊",
      icon: "none",
    });
  } else {
    jumpToUrl('Live', {
      inquiryId: props.info.id,
      isPhysician: "1",
    })
  }
}
</script>
<style scoped lang="scss" >
.appointment-card{
  position: relative;
  padding-bottom: 32rpx;
  box-sizing: border-box;
  background-color: white;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  .appointment-detail{
    display: flex;
    justify-content: space-between;
    padding: 24rpx 24rpx 24rpx 32rpx;
    .appointment-detail-left{
      .appointment-detail-left-t{
        padding-bottom: 22rpx;
        .appointment-detail-name{
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
          line-height: 36rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 12rpx;
        }
        .appointment-detail-title{
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 36rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 16rpx;
        }
        .appointment-detail-departmentName{
          font-weight: 400;
          font-size: 24rpx;
          color: #4BE092;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          background: #F6FDF9;
          border-radius: 198rpx 198rpx 198rpx 198rpx;
          border: 2rpx solid rgba(75, 224, 146, 0.6);
          padding: 0 16rpx;
        }
      }
      .appointment-detail-left-b{

        .appointment-detail-institutionName{
          font-weight: 400;
          font-size: 28rpx;
          color: #666666;
          line-height: 36rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .appointment-detail-right{

    }
  }
  .appointment-message{
    position: relative;
    background: #F8F8F8;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 16rpx 24rpx;
    margin: 0 24rpx 24rpx 24rpx;
    .appointment-detail-col{
      margin-bottom: 24rpx;
      overflow: hidden;
      .appointment-detail-col-l{
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .appointment-detail-col-r{
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .appointment-detail-col-conclusion{
      margin-bottom: 0;
    }
    .appointment-message-static{
      position: absolute;
      right: 0;
      top: 0;
      border-radius: 0 24rpx 0 24rpx;
      padding: 8rpx 24rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #FFFFFF;
      line-height: 32rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .appointment-btn{
    display: flex;
    justify-content: center;
  }
}
</style>
