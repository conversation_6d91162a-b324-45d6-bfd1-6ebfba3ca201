import type {AdministrationMethod, DosageFrequency} from "@/enum/prescriptionsEnum";

export interface presRpListDTO {
    //记录ID
    id: string,
    // 处方ID
    presId:string,
    // 商品ID
    productId:string,
    //商品通用名
    productName: string,
    //规格ID
    specId:string,
    //规格名称
    specName: string,
    //给药方式
    routeOfAdministration: AdministrationMethod,
    //给药方式
    route?: AdministrationMethod,
    //自定义给药方式，即其他给药方式
    routeOfAdministrationOther?:AdministrationMethod,
    //自定义给药方式，即其他给药方式
    custRoute: string,
    //用药频次
    freq?: DosageFrequency,
    //用药频次
    frequencyOfAdministration?:DosageFrequency,
    //自定义用药频次，即其他用药频次
    frequencyOfAdministrationOther?: string,
    //自定义用药频次，即其他用药频次
    custFreq?: string,
    //剂量
    dosage: number,
    //剂量单位
    dosageUnits?: number,
    //剂量单位
    units?: number,
    //购买数量
    count: number,
    /**
     * 药品的医嘱
     */
    advice?: string;
    /**
     * 中药剂数：西药无此属性，中药必填此参数
     */
    chineseDosage?: number;

}