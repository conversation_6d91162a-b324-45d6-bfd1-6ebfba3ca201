<template>
  <view class="consultation-card msg-module-card">
    <view class="consultation-card-header">
      <image
        :src="consultationCardIcon"
        class="consultation-card-header-icon"
        mode="widthFix"
      />
      <view class="consultation-card-header-title">问诊卡</view>
      <view class="consultation-card-header-tag">常规问诊</view>
    </view>
    <view class="consultation-card-body">
      <view class="consultation-card-body-item">
        <view class="consultation-card-body-item-title">患者:</view>
        <view class="consultation-card-body-item-content"
          >{{ consultationCardData.patientName }}
          （{{consultationCardData.patientSex}}
          {{ consultationCardData.patientAge }}岁）</view
        >
      </view>
      <view class="consultation-card-body-item">
        <view class="consultation-card-body-item-title">主诉:</view>
        <view class="consultation-card-body-item-content">{{
          consultationCardData.chiefComplaint
        }}</view>
      </view>
    </view>
    <van-divider custom-style="margin: 20rpx 0;" />
    <view class="consultation-card-footer">
      <van-icon name="warning-o" color="#999999" />
      <view class="consultation-card-footer-text">
        平台将保证您的隐私安全，请放心咨询&nbsp;&nbsp;&nbsp;&nbsp;
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import consultationCardIcon from "@/static/images/inquiry/consultationCardIcon.png";
import { CustomMessageTypeEnum } from "@/enum/IM";
import { CustomMessage } from "@/utils/imSdkUtils/sdkUtils";
const props = withDefaults(
  defineProps<{
    payload: CustomMessage["payload"];
  }>(),
  {
    payload: () => ({
      data: "",
      description: CustomMessageTypeEnum.PRES_CARD,
    }),
  }
);

const consultationCardData = computed(() => {
  return JSON.parse(JSON.parse(props.payload.data).content);
});
</script>

<style lang="scss" scoped>
@import "@/subPackages/Inquiry/css/dialogueStyle.scss";
.consultation-card,
.msg-module-card {
  &-footer {
    display: flex;
    align-items: center;
    gap: 16rpx;
    &-text {
      font-size: 24rpx;
      color: #999999;
    }
  }
}
</style>
