import {JRequest} from "@/services/index"
import { basicPlatformUrl } from "@/utils/urlUtils"
import type {PageVOType} from "@/subPackages/User/prescription/types";

// 用户信息接口地址
const enum UserApiEnum{
	getUserInfo="/applet/customerEntity/getCustomerInfo",   //获取用户信息
    editUserInfo="/applet/customerEntity/editCsInfo",       //修改用户信息
    authentication="/applet/customerEntity/updateCsAuth",    //身份认证
    updateAvatar="/common/upload",                      //上传文件
    getPres="/applet/pres/getPres",                     //获取待使用处方（于1.1.4版本更换）
    countPrescribed="/applet/pres/countPrescribed",     //获取待使用处方数量
    getCountMyOrders = "/applet/order/countMyOrders"    //统计我的订单数量
}

//获取用户信息
export async function getUserInfo(){
    return JRequest.get({
        url:UserApiEnum.getUserInfo,
        requestConfig:{
            withToken:false
        }
    })
}

// 修改用户信息
interface EditUserInfoParams{
    nickname?:string,
    img?:string,
}
export async function editUserInfo(params:EditUserInfoParams){
    return JRequest.put({
        url:UserApiEnum.editUserInfo,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 身份认证
interface AuthenticationParams{
    idNo:string,
    name:string,
}
export async function authentication(params:AuthenticationParams){
    return JRequest.put({
        url:UserApiEnum.authentication,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 上传文件
interface UpdateAvatarParams{
    files:File
}
export async function updateAvatar(params){
    return JRequest.post({
        url:UserApiEnum.updateAvatar,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 获取待使用处方
export async function getPres(){
    return JRequest.post({
        url:UserApiEnum.getPres,
        requestConfig:{
            withToken:false
        }
    })
}

// 统计待使用处方数量
export async function getCountPrescribed(){
    return JRequest.get({
        url:UserApiEnum.countPrescribed,
        requestConfig:{
            withToken:false
        }
    })
}

// 统计我的订单数量
export async function getCountMyOrders(){
    return JRequest.get({
        url:UserApiEnum.getCountMyOrders,
        requestConfig:{
            withToken:false
        }
    })
}

// 用户地址信息 接口地址
const enum UserAddressApiEnum{
    getAddressList="/applet/customerAddress/page",              //获取地址列表
    addAddress="/applet/customerAddress/add",                   //新增地址
    editAddress="/applet/customerAddress/update",               //编辑地址
    deleteAddress="/applet/customerAddress/remove",             //删除地址
    defaultAddress="/applet/customerAddress/updateIsDefault",   //设置默认地址
    addressData= "/addressEntity/listByCode"                    //获取地址数据  
}

// 获取地址信息
export async function getAddressList(params){
    return JRequest.post({
        url:UserAddressApiEnum.getAddressList,
        params,
        requestConfig:{
            withToken:false
        }
    })
}

// 新增地址
export interface AddAddressParams {
    address?: null | string;
    area?: null | string;
    areaId?: number | string;
    cityId?: number | string;
    cityName?: null | string;
    company?: null | string;
    companyId?: number | string;
    customerId?: number | string;
    id?: number | string;
    isDefault: number | null;
    mobile: null | string;
    name: null | string;
    province?: null | string;
    provinceId?: number | string;
    town?: null | string;
    townId?: number | string;
}
export async function addAddress(params:AddAddressParams){
    return JRequest.post({
        url:UserAddressApiEnum.addAddress,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 编辑地址
export async function editAddress(params:AddAddressParams){
    return JRequest.put({
        url:UserAddressApiEnum.editAddress,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 删除地址
interface DeleteAddressParams{
    id:number | string,
}
export async function deleteAddress(params:DeleteAddressParams){
    return JRequest.post({
        url:UserAddressApiEnum.deleteAddress,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 设置默认地址
interface DefaultAddressParams{
    id:number | string,
    isDefault:number
}
export async function defaultAddress(params:DefaultAddressParams){
    return JRequest.put({
        url:UserAddressApiEnum.defaultAddress,
        params:{data:params},
        requestConfig:{
            withToken:false
        }
    })
}

// 获取地址数据
interface AddressDataParams{
    code:string,
    cateType:number
}
export async function addressData(params:AddressDataParams){
    return JRequest.get({
        url:`${UserAddressApiEnum.addressData}`,
        params:{
            parentCode:params.code,
            cateType:params.cateType
        },
        requestConfig:{
            withToken:false
        }
    })
}

// 用户地址信息 接口地址
const enum UserMedicalApiEnum{
    // 问诊单列表展示
    presNew="/applet/pres/page/new",
    // 问诊详情
    getPresDetail="/applet/pres/getPresDetail",
    // 取消问诊单
    cancelPres="/applet/pres/cancelPres",
    // 我的医生列表
    myPreBook="/applet/doctorEntity/page/myPreBook",
    // 医助后台问诊单
    physAssist="/applet/pres/page/physAssist",
    // 我的预约列表
    presMyPreBook="/applet/pres/page/myPreBook",
}

interface presNewParams{
    pageVO:PageVOType
}

// 问诊单列表展示
export async function presNew(params:presNewParams){
    return JRequest.post({
        url:UserMedicalApiEnum.presNew,
        params:{
            pageVO:params.pageVO
        },
        requestConfig:{
            withToken:false
        }
    })
}

// 问诊详情
export async function getPresDetail(id:string){
    return JRequest.get({
        url:`${UserMedicalApiEnum.getPresDetail}?id=${id}`,
        requestConfig:{
            withToken:false
        }
    })
}

// 取消问诊单
export async function cancelPres(id:string){
    return JRequest.delete({
        url:`${UserMedicalApiEnum.cancelPres}?id=${id}`,
        requestConfig:{
            withToken:false
        }
    })
}

// 我的医生列表
export async function myPreBook(params:presNewParams){
    return JRequest.post({
        url:UserMedicalApiEnum.myPreBook,
        params:{
            pageVO:params.pageVO
        },
        requestConfig:{
            withToken:false
        }
    })
}

// 医助问诊单列表
export async function physAssist(params:presNewParams){
    return JRequest.post({
        url:UserMedicalApiEnum.physAssist,
        params:{
            pageVO:params.pageVO
        },
        requestConfig:{
            withToken:false
        }
    })
}
// 我的预约列表
export async function presMyPreBook(params:presNewParams){
    return JRequest.post({
        url:UserMedicalApiEnum.presMyPreBook,
        params:{
            pageVO:params.pageVO
        },
        requestConfig:{
            withToken:false
        }
    })
}