@mixin doctor-card-content-style {
  padding: 24rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 24rpx;
  margin-top: 24rpx;
}

.doctor-card-avatar-online-border {
  &::before {
    content: "";
    position: absolute;
    top: -2px;
    right: -2px;
    bottom: -2px;
    left: -2px;
    z-index: 1;
    border-radius: inherit;
    background: linear-gradient(
      180deg,
      rgba(255, 205, 205, 0.3),
      rgba(255, 77, 77, 1)
    );
  }
}
.doctor-card-avatar {
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;

  .doctor-card-avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
  }
  .doctor-card-avatar-online {
    position: absolute;
    bottom: -14rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff4d4d;
    color: #fff;
    font-size: 22rpx;
    height: 32rpx;
    line-height: 32rpx;
    width: 64rpx;
    border-radius: 16rpx;
    text-align: center;
    z-index: 2;
  }
}
.doctor-card-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .doctor-card-content-header-left {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    .doctor-card-info-user {
      display: flex;
      align-items: flex-end;
      .doctor-card-info-user-name {
        flex-shrink: 0;

        font-size: 50rpx;
        color: #333;
        font-weight: bold;
        line-height: 48rpx;
      }
      .doctor-card-info-user-title {
        flex-shrink: 0;
        font-size: 28rpx;
        color: #666666;
        margin-left: 12rpx;
      }
      .doctor-card-info-user-department {
        flex-shrink: 0;
        background: #4be092 rgba(255, 255, 255, 0.95);
        border-radius: 198rpx 198rpx 198rpx 198rpx;
        border: 2rpx solid rgba(75, 224, 146, 0.6);
        font-size: 24rpx;
        text-align: center;
        color: #4be092;
        margin-left: 16rpx;
        padding: 4rpx 16rpx;
      }
    }
  }
}
.doctor-card-content {
  .doctor-card-info {
    margin-top: 34rpx;
    .doctor-card-specialize-title {
      color: #333333;
    }
  }
}
.doctor-card-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  .doctor-card-info-user {
    display: flex;
    align-items: flex-end;
    .doctor-card-info-user-name {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
    .doctor-card-info-user-title {
      font-size: 28rpx;
      color: #333;
      margin-left: 8rpx;
    }
    .doctor-card-info-user-department {
      background: #4be092 rgba(255, 255, 255, 0.95);
      border-radius: 198rpx 198rpx 198rpx 198rpx;
      border: 2rpx solid rgba(75, 224, 146, 0.6);
      font-size: 24rpx;
      text-align: center;
      color: #4be092;
      margin-left: 16rpx;
      padding: 4rpx 16rpx;
    }
  }

  .doctor-card-hospital {
    color: #666666;
    font-size: 28rpx;
  }
  .doctor-card-specialize {
    font-size: 28rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 48rpx;
    .doctor-card-specialize-title {
      color: #999999;
    }
    .doctor-card-specialize-content {
      color: #666666;
    }
  }
}

/** 医生服务样式 图文&视频 */
.doctor-card-footer-inquiry {
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 24rpx;
  box-sizing: border-box;
  margin-top: 24rpx;
  display: flex;
  gap: 24rpx;
  .doctor-card-footer-left {
    width: 56rpx;
    height: 56rpx;
    flex-shrink: 0;
    .doctor-card-footer-left-image {
      width: 100%;
      height: 100%;
    }
  }
  .doctor-card-footer-right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 10rpx;
    width: 100%;
    .doctor-card-footer-right-handle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .doctor-card-footer-right-introduce {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        gap: 24rpx;
        .doctor-card-footer-right-title {
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
        }
        .doctor-card-footer-right-price {
          font-size: 24rpx;
          color: #999999;
          .price-number {
            color: #ff4d4d;
            font-family: "DINPro";
          }
        }
      }
    }
    .doctor-card-footer-right-describe {
      line-height: 36rpx;
      color: #666666;
      font-size: 24rpx;
    }
  }
}
.doctor-card-footer-inquiry-imgText {
  border: 2rpx solid rgba(22, 119, 255, 0.7);
  background: #eff7ff;
}
.doctor-card-footer-inquiry-video {
  border: 2rpx solid rgba(75, 224, 146, 0.7);
  background: #f6fdf9;
}
