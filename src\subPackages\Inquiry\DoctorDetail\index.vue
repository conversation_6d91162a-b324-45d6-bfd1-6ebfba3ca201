<template>
  <view class="doctor-home-page" :style="customStyle">
    <Navigation
      navBackgroundColor="transparent"
      :useLeft="true"
      :showLeft="true"
      @arrowLeftHander="arrowLeftHander"
    >
      <template #center>{{ doctorDetailData.doctorName }}</template>
      <template #left>
        <view class="right-icon">
          <!-- <van-icon name="arrow-left" size="40rpx" custom-style="color: #000000"/> -->
          <van-icon
            :name="isFirstPage ? 'wap-home-o' : 'arrow-left'"
            size="40rpx"
            custom-style="color: #000000"
          />
        </view>
      </template>
    </Navigation>
    <view class="doctor-home-page-content">
      <van-skeleton v-if="loading" row="1" :loading="loading" />
      <view
        v-else-if="doctorDetailData.doctorCode"
        class="doctor-content-header"
      >
        <view class="doctor-content-header-status"> 已实名认证</view>
        <view class="doctor-content-header-certificate">
          资格证书编号：{{ doctorDetailData.doctorCode }}
        </view>
      </view>
      <view class="doctor-card-content">
        <van-skeleton v-if="loading" row="3" :loading="loading" />

        <view v-if="!loading" class="doctor-card-content-header">
          <view class="doctor-card-content-header-left">
            <view class="doctor-card-info-user">
              <view class="doctor-card-info-user-name">{{
                doctorDetailData.doctorName
              }}</view>
              <view class="doctor-card-info-user-title">{{
                doctorTitleMap[doctorDetailData.title]
              }}</view>
              <view class="doctor-card-info-user-department">{{
                doctorDetailData.departmentName
              }}</view>
            </view>
            <view class="doctor-card-hospital">{{
              doctorDetailData.institutionName
            }}</view>
          </view>

          <view
            class="doctor-card-avatar"
            :class="{
              'doctor-card-avatar-online-border':
                doctorDetailData.onlineStatus == 1,
            }"
          >
            <image
              :src="doctorDetailData.img || defaultAvatar"
              class="doctor-card-avatar-image"
              mode="scaleToFill"
              @error="handleAvatarError"
            />
            <view
              class="doctor-card-avatar-online"
              v-if="doctorDetailData.onlineStatus == 1"
              >在线</view
            >
          </view>
        </view>

        <view v-if="!loading" class="doctor-card-info">
          <view
            class="doctor-card-specialize"
            @click="showPopup = true"
            v-if="doctorDetailData.beGoodAt"
          >
            <text class="doctor-card-specialize-title">擅长：</text>
            <text class="doctor-card-specialize-content">{{
              doctorDetailData.beGoodAt
            }}</text>
          </view>
          <van-divider custom-style="margin:10rpx 0rpx" />
          <view
            class="doctor-card-specialize"
            @click="showPopup = true"
            v-if="doctorDetailData.introduction"
          >
            <text class="doctor-card-specialize-title">简介：</text>
            <text class="doctor-card-specialize-content">{{
              doctorDetailData.introduction
            }}</text>
          </view>
        </view>
        <view class="doctor-card-share" v-if="!loading">
          <van-button
            :icon="shareIcon"
            color="#F3F3F3"
            round
            type="info"
            open-type="share"
            customStyle="color:#1677ff;font-weight:bold;"
          >
            推荐给朋友
          </van-button>
        </view>
      </view>
      <view
        class="doctor-card-footer"
        v-if="doctorDetailData.isPictureText == 1 || doctorDetailData.isVideo == 1"
      >
        <view class="doctor-card-footer-title"> 服务 </view>
       <doctorServices :doctorDetailData="doctorDetailData" />
      </view>
    </view>
  </view>
  <DescriptionPopup
    v-model:show="showPopup"
    :speciality="doctorDetailData.beGoodAt"
    :description="doctorDetailData.introduction"
  />
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from "vue";
import { onLoad, onShareAppMessage } from "@dcloudio/uni-app";
import Navigation from "@/components/Navigation/index.vue";
import doctorServices from "@/subPackages/Inquiry/components/doctorServices.vue";
import DescriptionPopup from "./components/descriptionPopup.vue";
import doctorDetailBg from "@/static/images/inquiry/doctorDetailBg.png";
import { useRectInfo } from "@/hooks";
import shareIcon from "@/static/images/inquiry/shareIcon.png";
import {
  navigateBack,
  reLaunch,
  navigateTo,
} from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { routesMap } from "@/routes/maps";
import { type DoctorEntityPageDTO, doctorTitleMap, InquiryTypeEnum } from "../type";
import { doctorDetail, DoctorDetail } from "@/services/api/inquiry";
import { isDef, isEmpty } from "@/utils/isUtils";
import {
  systemStore,
  useSystemStoreWithoutSetup,
} from "@/stores/modules/system";
const _systemStore = useSystemStoreWithoutSetup();

import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";

const { rectInfo } = useRectInfo();
const arrowLeftHander = async () => {
  try {
    await navigateBack();
  } catch (error) {
    reLaunch({
      url: RouteName.Inquiry,
    });
  }
};

const customStyle = computed(() => {
  const { mBSize, rectbtnHeight, mTSize } = rectInfo.value;

  return {
    paddingTop: `${mBSize + rectbtnHeight + mTSize}px`,
    backgroundImage: `url(${doctorDetailBg})`,
  };
});


/** 判断当前页面是不是第一页 */
const isFirstPage = computed(() => {
  const pages = getCurrentPages();
  return pages.length == 1;
});

/** 分享 */
onShareAppMessage(() => {
  return {
    title: `${doctorDetailData.value.doctorName}医生 ${
      doctorTitleMap[doctorDetailData.value.title]
    } ${doctorDetailData.value.departmentName}`,
    path: `${routesMap[RouteName.InquiryDoctorDetail].path}?doctorId=${
      doctorDetailData.value.id
    }`,
  };
});

onLoad((options) => {
  getDoctorDetail(options.doctorId);
  if (isDef(options.sharingInfo) && !isEmpty(options.sharingInfo)) {
    _systemStore.setSharingInfoKey(options.sharingInfo);
  }
});

const handleAvatarError = () => {
  doctorDetailData.value.img = defaultAvatar;
};

const doctorDetailData = ref<DoctorDetail>({} as DoctorDetail);

/** 获取医生详情 */
const loading = ref(true);
const getDoctorDetail = (doctorId: string) => {
  loading.value = true;
  doctorDetail(doctorId)
    .then((res) => {
      doctorDetailData.value = res;
      loading.value = false;
    })
    .catch((err) => {
      uni.showToast({
        title: `获取医生详情失败${err}`,
        icon: "none",
      });
    });
};

const showPopup = ref(false);
</script>

<style lang="scss" scoped>
@import "../css/doctorStyle.scss";
@import "@/static/css/fonts.scss";

.doctor-home-page {
  height: 100vh;
  background-size: 100% 35%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  .doctor-home-page-content {
    padding: 24rpx;
    box-sizing: border-box;
    .doctor-content-header {
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      background: rgba(255, 255, 255, 0.75);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      height: 72rpx;
      font-weight: bold;
      font-size: 28rpx;
      .doctor-content-header-status {
        color: #1677ff;
      }
      .doctor-content-header-certificate {
      }
    }
    .doctor-card-content {
      @include doctor-card-content-style;
    }
    .doctor-card-share {
      display: flex;
      justify-content: flex-end;
      margin-top: 24rpx;
    }

    .doctor-card-footer {
      @include doctor-card-content-style;
      .doctor-card-footer-title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
      }
    }
  }
}
</style>

<style>
page {
  background: #f3f3f3;
}
</style>
