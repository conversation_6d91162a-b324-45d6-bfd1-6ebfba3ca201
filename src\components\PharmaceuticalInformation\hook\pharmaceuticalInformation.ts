import {computed, ref} from 'vue'
import {PrescriptionStatus} from "@/enum/prescriptionsEnum";

export default function pharmaceuticalInformation() {
    // 给药方式列表
    const routeOfAdministration_list = ["其他", "口服", "外用", "吸入", "舌下给药", "直肠给药", "静脉注射", "肌肉注射", "皮下注射","水煎服、冲服"]
    // 用药频次列表
    const frequencyOfAdministration_list = ["其他", "1次/天", "2次/天", "3次/天", "4次/天", "1次/小时", "1次/3小时",
        "1次/6小时", "1次/8小时", "1次/12小时", "1次/周", "2次/周", "隔天1次", "每月1次", "每3月1次","每日一剂","每日两剂","隔日一剂"]
    // 剂量单位
    const dosageUnits_list = ['粒', '片', '包', '袋', '盒', '瓶', '板', '支', '台', '件', '套', '个', '罐', '管', '双', '卷', '把',
        '对', '条', '贴', '张', '只', '筒', '组', '付', '本', 'μg', 'mg', 'g', 'kg', 'ml', 'l']
    function getRouteProperty(item, str1, str2) {
        if (item[str1]) {
            return item[str1];
        } else if (item[str2]) {
            return item[str2];
        } else {
            return null;
        }
    }
    return {
        routeOfAdministration_list,
        frequencyOfAdministration_list,
        dosageUnits_list,
        getRouteProperty
    }
}