<template>
  <view class="prescription-card msg-module-card">
    <view class="prescription-card-header">
      <image :src="prescriptionCardIcon" class="prescription-card-header-icon" mode="widthFix" />
      <view class="prescription-card-header-title">处方单</view>
      <view class="prescription-card-header-link" @click="handleDetail">详情<van-icon name="arrow" /></view>
    </view>
    <view class="prescription-card-body">
      <view class="prescription-card-body-item">
        <view class="prescription-card-body-item-title">患者</view>
        <view class="prescription-card-body-item-content">
          {{ prescriptionCardData.patientName }}
          （{{prescriptionCardData.patientSex}}
          {{ prescriptionCardData.patientAge }}岁）
        </view>
      </view>
      <view class="prescription-card-body-item">
        <view class="prescription-card-body-item-title">诊断</view>
        <view class="prescription-card-body-item-content">{{prescriptionCardData.clinicalDiagnosis}}</view>
      </view>
      <view class="prescription-card-body-item">
        <view class="prescription-card-body-item-title">Rp</view>
        <view class="prescription-card-body-item-content">
          <PharmaceuticalInformation :Pharmaceutical_information_list="prescriptionCardData.presRpList" :prescriptionDrugType="prescriptionCardData.type" :rpSize="prescriptionCardData.rpSize" />
        </view>
      </view>
      <van-divider custom-style="margin: 20rpx 0;" />
      <view class="prescription-card-footer">
        <van-button  color="#1677FF" size="small" type="info" @click.stop="handlePay" >
        去支付
        </van-button>
      </view>
      
    </view>
  </view>
  <drugAbnormal v-model:show="showDrugAbnormal" :errList="errList" />
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import prescriptionCardIcon from "@/static/images/inquiry/prescriptionCardIcon.png";
import PharmaceuticalInformation from "@/components/PharmaceuticalInformation/index.vue";
import drugAbnormal from "@/subPackages/Inquiry/components/drugAbnormal.vue";
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { PresConfirmOrder } from "@/services/api/Order";
import { CustomMessageTypeEnum } from "@/enum/IM";
import { PrescriptionDrugType } from "@/enum/prescriptionsEnum";
import { CustomMessage } from "@/utils/imSdkUtils/sdkUtils";
const props = withDefaults(defineProps<{
    payload: CustomMessage['payload']
}>(), {
    payload: () => ({
        data: "",
        description: CustomMessageTypeEnum.FORMULARY_CARD

    })
})

const enum PrescriptionErrEnum {
  /** 已使用 */
  USED = "USED",
  /** 已失效 */
  EXPIRED = "EXPIRED",
  /** 库存不足 */
  UNDERSTOCK = "UNDERSTOCK"
}

const handleDetail = () => {
  navigateTo({
    url: RouteName.PrescriptionFormDetail,
    props: {
      id: prescriptionCardData.value.id
    }
  })
}
const prescriptionCardData = computed(() => {
  return JSON.parse(JSON.parse(props.payload.data).content);
});

const errList = ref<string[]>([])

const showDrugAbnormal = ref(false)
const handlePay = () => {
  // navigateTo(RouteName.PAYMENT);
  // showDrugAbnormal.value = true;
  uni.showLoading({
    title:'加载中',
    mask:true,
    icon:'loading'
  })
  PresConfirmOrder(prescriptionCardData.value.id).then(res => {
    console.log(res)
    navigateTo({
      url: RouteName.OrderConfirm,
      props: {
        orderInfo: encodeURIComponent(JSON.stringify(res)),
        presVersionType:1
      }
    })
    uni.hideLoading()
  }).catch(err => {
    uni.hideLoading()
    let error;
    try {
      error = JSON.parse(err)
    } catch (error) {
      uni.showToast({
        title: `创建订单失败：${err}`,
        icon: 'none'
      })
      return
    }
    if(error.formularyErrorEnum == PrescriptionErrEnum.UNDERSTOCK){
      showDrugAbnormal.value = true;
      const list = error.errorMsg.split("\n")
      errList.value = list.map(item => item.slice(2))
      console.log(errList.value,'---');
    }else if(error.formularyErrorEnum == PrescriptionErrEnum.USED || error.formularyErrorEnum == PrescriptionErrEnum.EXPIRED){
      uni.showToast({
        title: `处方单${error.formularyErrorEnum == PrescriptionErrEnum.USED ? "已使用" : "已失效"}`,
        icon: 'none'
      })
    }
  })
  

}

const Pharmaceutical_information_list = []

</script>

<style lang="scss" scoped>
@import "@/subPackages/Inquiry/css/dialogueStyle.scss";
.prescription-card{
  &-footer{
    display: flex;
    justify-content: flex-end;
  }
}

</style>
