<template>
  <view class="recipeHintBox" v-if="prescriptionInfo.formularyCount > 0">
    <view class="recipeHint">
      <van-image :src="recipeIcon" width="72rpx" height="72rpx"></van-image>
      <view class="hintTitle">您有处方待支付</view>
      <view class="hintBtn" @click="toPreList">去支付</view>
    </view>
  </view>
  <drugAbnormal v-model:show="showDrugAbnormal" :errList="errList" />

</template>

<script setup lang="ts">
import { getPresToPaid, type PresInfo } from "@/services/api/user";
import { ref } from "vue";
import { navigateTo, switchTab } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import recipeIcon from "@/static/images/user/recipeIcon.png";
import { onShow } from "@dcloudio/uni-app";
import drugAbnormal from "@/subPackages/Inquiry/components/drugAbnormal.vue";

import {
  PresConfirmOrder,
  TherapyPres,
  PrescriptionErrEnum,
} from "@/services/api/Order";
// import { TherapyPres } from "@/services/api/Order";

/** 去使用回调
 * @description 如果有多个处方就跳转处方列表 只有一个处方的情况下，问诊处方就调用问诊的处方下单接口,旧版本处方判断是否有订单号，已经有订单号的直接跳转收银台
 */
const toPreList = () => {
  if (prescriptionInfo.value.formularyCount > 1) {
    navigateTo({
      url: RouteName.ListOfPrescriptions,
    });
    return;
  }
  // 有订单直接跳转收银台页
  if (prescriptionInfo.value.orderCode) {
    navigateTo({
      url: RouteName.Pay,
      props: {
        orderCode: prescriptionInfo.value.orderCode,
        placeOrder: 0,
      },
    });
    return;
  }
  // versionType == 1 问诊处方
  if (prescriptionInfo.value.versionType == 1) {
    createOrderFormulary();
  } else {
    // 只有疗法处方才会处方待使用 但是无关联订单 所有直接调用疗法开单
    createTherapyOrder();
  }
};

/** 问诊处方开单 */
const errList = ref<string[]>([])

const showDrugAbnormal = ref(false)
const createOrderFormulary = () => {
  uni.showLoading({
    title: "加载中...",
    mask: true,
  });
  PresConfirmOrder(prescriptionInfo.value.formularyId as string)
    .then((res) => {
      navigateTo({
        url: RouteName.OrderConfirm,
        props: {
          orderInfo: encodeURIComponent(JSON.stringify(res)),
          presVersionType: 1,
        },
      });
    })
    .catch((err) => {
      uni.hideLoading();
      let error;
      try {
        error = JSON.parse(err);
      } catch (error) {
        uni.showToast({
          title: `创建订单失败：${err}`,
          icon: "none",
        });
        return;
      }
      if (error.formularyErrorEnum == PrescriptionErrEnum.UNDERSTOCK) {
        showDrugAbnormal.value = true;
        const list = error.errorMsg.split("\n")
        errList.value = list.map(item => item.slice(2))
      } else if (
        error.formularyErrorEnum == PrescriptionErrEnum.USED ||
        error.formularyErrorEnum == PrescriptionErrEnum.EXPIRED
      ) {
        uni.showToast({
          title: `处方单${
            error.formularyErrorEnum == PrescriptionErrEnum.USED
              ? "已使用"
              : "已失效"
          }`,
          icon: "none",
        });
      }
    })
    .finally(() => {
      uni.hideLoading();
    });
};
/** 疗法开单 */
const createTherapyOrder = () => {
  uni.showLoading({
    title: "加载中...",
    mask: true,
  });
  TherapyPres(prescriptionInfo.value.formularyId as number)
    .then((res) => {
      navigateTo({
        url: RouteName.OrderConfirm,
        props: {
          orderInfo: encodeURIComponent(JSON.stringify(res)),
          presVersionType: 0,
        },
      });
    })
    .catch((err) => {
      uni.showToast({
        title: `创建订单失败：${err}`,
        icon: "none",
      });
    })
    .finally(() => {
      uni.hideLoading();
    });
};

// 是否有处方
const prescriptionInfo = ref<PresInfo>({
  formularyCount: 0,
});
const getPresFn = () => {
  getPresToPaid()
    .then((res) => {
      prescriptionInfo.value = res;
    })
    .catch((err) => {
      console.log(err);
    });
  // getPres()
  //   .then((res) => {
  //     preId.value = res?.id || "";
  //     recipeHint.value = !!res?.id;
  //   })
  //   .catch((err) => {
  //     console.log(err);
  //   });
};
onShow(() => {
  getPresFn();
});
</script>

<style lang="scss" scoped>
.recipeHintBox {
  padding: 24rpx;

  .recipeHint {
    border-radius: 48rpx;
    display: grid;
    grid-template-columns: 50rpx 1fr 128rpx;
    align-items: center;
    height: 96rpx;
    // margin-top: 40rpx;
    padding: 0rpx 24rpx;
    box-sizing: border-box;
    gap: 20rpx;
    background: linear-gradient(90deg, #faf0e7, #ffe2c8);
    border: 2rpx solid #ffffff;
    box-shadow: 0px 6px 13px 0px rgba(230, 184, 142, 0.46);
    .hintTitle {
      font-size: 32rpx;
      font-weight: bold;
      color: #6c350f;
    }

    .hintBtn {
      width: 128rpx;
      height: 56rpx;
      line-height: 56rpx;
      border-radius: 28rpx;
      background: linear-gradient(270deg, #f28829 0%, #f25d3e 100%);
      color: #ffffff;
      text-align: center;
      font-size: 24rpx;
    }
  }
}
</style>
