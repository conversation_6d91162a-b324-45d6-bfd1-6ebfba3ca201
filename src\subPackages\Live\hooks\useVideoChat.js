import { ref, reactive, computed } from "vue";
import { navigateBack, reLaunch } from "@/routes/utils/navigateUtils";
import VolcMiniappSdk from "../lib/VolcEngineRTC_MiniApp_3.2.1";
import { isEmpty } from "@/utils/isUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
const { Client, EVENTS } = VolcMiniappSdk;
/** 视频问诊推拉流地址 */
const videoChatStreamUrl = reactive({
  /** 推流地址 */
  pushUrl: "",
  /** 拉流地址 */
  pullUrlMap: {},
});
const client = ref({});
const isLoading = ref(false);
const volcMiniappInfo = reactive({
  /** appid */
  appid: "683a5e93cd8a350179707168",
  /** token */
  token:
    "001683a5e93cd8a350179707168QwD486ICnlM+aB6OR2gIAHRlc3QxMjM0BQB1c2VyMgYAAAAejkdoAQAejkdoAgAejkdoAwAejkdoBAAejkdoBQAejkdoIABwaeWAbxW/TwEPddUV5sTu1aiQnZPK1szlFGGY1rjayA==",
  /** 房间id */
  roomId: "test1234",
  /** 用户id */
  uid: "user2",
});

/**
 * 当前视频模式 1: 单人模式 2: 双人模式 3: 三人模式
 */
const getCurrentVideoMode = computed(() => {
  const pullUrlMap = videoChatStreamUrl.pullUrlMap;
  return Object.keys(pullUrlMap).length + 1;
});

/** 媒体状态 */
const mediaState = reactive({
  /** 是否静音 */
  isMute: true,
  /** 前置或后置 front/back */
  frontOrBack: "front",
  /** 是否开启扬声器 */
  isSpeakerOn: true,
});

setInterval(() => {
  console.warn(
    videoChatStreamUrl,
    "videoChatStreamUrl",
    getCurrentVideoMode.value
  );
}, 10000);
export default function useVideoChat() {
  /** 初始化 */
  const init = async (info) => {
    console.log(EVENTS, "EVENTS");
    videoChatStreamUrl.pullUrlMap = {};
    Object.assign(volcMiniappInfo, info);
    isLoading.value = true;

    /** 如果实例存在则注销监听并且销毁 */
    if (!isEmpty(client.value) && client.value.destroy) {
      removeEventListener();
      // 等待旧实例完全销毁
      // await new Promise((resolve) => {
      //   client.value.destroy(() => {
      //     console.log("旧实例销毁成功");
      //     resolve();
      //   });
      // });

      await client.value.destroy(() => {
          console.log("旧实例销毁成功");
          // resolve();
        });
    }

    // 确保旧实例完全销毁后再创建新实例
    client.value = new Client();
    client.value.init(volcMiniappInfo.appid, onInitSuccess, (err) => {
      console.error("火山视频初始化失败", err);
      uni.showToast({
        title: `初始化失败: ${err.reason}`,
        icon: "none",
      });
    });
  };

  /** 初始化成功 开启监听 加入房间 */
  const onInitSuccess = () => {
    addEventListener();
    client.value.join(
      volcMiniappInfo.token,
      volcMiniappInfo.roomId,
      volcMiniappInfo.uid,
      (data) => {
        console.log("加入房间成功", data);
        publish();
      },
      (err) => {
        console.error("加入房间失败", err);
        uni.showToast({
          title: `加入房间失败: ${err.code}`,
          icon: "none",
        });
        isLoading.value = false;
      }
    );
  };

  /** 发布音视频流 */
  function publish() {
    try {
      client.value.publish(
        (url) => {
          console.log("推流地址", url);
          videoChatStreamUrl.pushUrl = url;
          mediaState.isMute = false;
          setTimeout(() => {
            const livePushRef = getLivePusherContext();
            // console.log(livePushRef, "+++++++++");

            livePushRef.start();
            // livePushRef.enableMic(true);
          }, 2000);
        },
        (err) => {
          throw err;
        }
      );
    } catch (error) {
      uni.showToast({
        title: `推流失败: ${error.reason}-${error.code}`,
        icon: "none",
      });
    }
  }

  /** 停止发布音视频流 */
  async function unpublish() {
    try {
      await client.value.unpublish();
      console.log("停止发布成功");
    } catch (error) {
      console.error("停止发布失败", error);
      // uni.showToast({
      //   title: `停止发布失败: ${error.reason}`,
      //   icon: "none",
      // });
    }
  }

  /** 获取pusher组件 */
  function getLivePusherContext() {
    return wx.createLivePusherContext("live-pusher");
  }

  /**
   * 订阅远端用户的音视频流
   * @param {string} uid 远端用户的唯一标识
   * @param {boolean} screen 是否屏幕共享流
   * @param {Object} streamPublishState 远端用户的音视频流状态
   * @param {boolean} streamPublishState.video 是否订阅视频流
   * @param {boolean} streamPublishState.audio 是否订阅音频流
   */
  function subscribe(
    uid,
    screen,
    streamPublishState = { video: true, audio: true }
  ) {
    client.value.subscribe(
      uid,
      {
        ...streamPublishState,
        screen,
      },
      (url) => {
        const pullConfig = {
          uid: uid,
          streamType: streamPublishState,
          url: url,
        };
        videoChatStreamUrl.pullUrlMap[uid] = pullConfig;
        console.log(`${uid}拉流地址`, pullConfig);
      },
      (err) => {
        console.error(`${uid}拉流失败`, err);
        uni.showToast({
          title: `拉流失败: ${err.reason}`,
          icon: "none",
        });
      }
    );
  }

  /**
   * 退出房间
   * @param {boolean} isHangUp 是否挂断按钮触发
   * */
  const handleHangUp = async (isHangUp = true) => {
    try {
      /** 如果实例不纯在直接抛出异常 */
      if (isEmpty(client.value) || !client.value.destroy) throw new Error("实例不存在");
      await unpublish();
      client.value.destroy(
        () => {
          console.log("离开房间并销毁成功");
        },
        (err) => {
          uni.showToast({
            title: `销毁实例失败: ${err.reason}`,
            icon: "none",
          });
          throw err;
        }
      );
    } catch (error) {
      console.log(error, "取消发布或销毁实例失败");
    }
    if (isHangUp) {
      try {
        await navigateBack();
      } catch (error) {
        reLaunch({
          url: RouteName.Inquiry,
        })
      }
    }
  };

  /** 静音切换 */
  const handleMute = () => {
    client.value.muteLocal(
      {
        audio: !mediaState.isMute,
        video: false,
      },
      () => {
        mediaState.isMute = !mediaState.isMute;
      },
      (err) => {
        console.error("静音切换失败", err);
        uni.showToast({
          title: `静音切换失败: ${err.reason}`,
          icon: "none",
        });
      }
    );
  };

  /** 开启或关闭扬声器 */
  const handleSpeaker = () => {
    mediaState.isSpeakerOn = !mediaState.isSpeakerOn;
  };

  /** 切换前置或后置摄像头 */
  const handleSwitchCamera = () => {
    const livePushRef = getLivePusherContext();
    livePushRef.switchCamera();
    mediaState.frontOrBack == "front"
      ? (mediaState.frontOrBack = "back")
      : (mediaState.frontOrBack = "front");
  };

  /** 远端用户进入房间回调 */
  const onPeerOnline = (data) => {
    console.log("远端用户进入房间", data);
  };

  /** 远端用户离开房间回调 */
  const onPeerLeave = (data) => {
    console.log("远端用户离开房间", data);
    const { uid } = data;
    delete videoChatStreamUrl.pullUrlMap[uid];
  };

  /** WebSocket 错误回调 */
  const liveError = (err) => {
    console.error("WebSocket 错误回调", err);
  };

  /** 远端用户发布音视频流 */
  const onPeerPublish = (data) => {
    console.log("远端用户发布音视频流", data);
    const { uid, streamPublishState, screen } = data;
    subscribe(uid, screen, streamPublishState);
  };

  /** 远端用户停止发布回调 */
  const onPeerUnpublish = (data) => {
    console.log("远端用户停止发布", data);
    const { uid } = data;
    delete videoChatStreamUrl.pullUrlMap[uid];
    if (Object.keys(videoChatStreamUrl.pullUrlMap).length > 0) {
      const refreshId = Object.keys(videoChatStreamUrl.pullUrlMap)[0];
      delete videoChatStreamUrl.pullUrlMap[refreshId];
      subscribe(refreshId, false, { video: true, audio: true });
    }
  };

  /**
   * 推流或拉流地址更新
   * 断网重连成功后，将重新发布/订阅
   * 将地址重新赋值
   */
  const onPeerStreamUpdated = (data) => {
    console.log("推流或拉流地址更新", data);

    const { url, uid, screen } = data;
    if (uid == volcMiniappInfo.uid) {
      // 自己的推流地址更新
      videoChatStreamUrl.pushUrl = url;
      return;
    }
    videoChatStreamUrl.pullUrlMap[uid].url = url;
  };

  /** 推流或拉流异常 */
  const onPeerStreamError = async (data) => {
    console.error("推流或拉流异常", data);
    const { uid } = data;
    if (uid == volcMiniappInfo.uid) {
      await unpublish();
      publish(); // 自己的推流异常，重新发布
    } else {
      // delete videoChatStreamUrl.pullUrlMap[data.uid];
      subscribe(data.uid, data.screen);
    }
  };

  /** 异地登录被挤出 */
  const onClientBanned = (data) => {
    uni.showToast({
      title: "问诊已结束，或在异地登录",
      icon: "none",
    });
    setTimeout(() => {
      handleHangUp();
    }, 1000);
  };

  /** 远端关闭音频 */
  const onPeerMuteAudio = (data) => {
    console.log("远端关闭音频", data);
    const { uid } = data;
    videoChatStreamUrl.pullUrlMap[uid].streamType.audio = false;
  };

  /** 远端开启音频 */
  const onPeerUnmuteAudio = (data) => {
    console.log("远端开启音频", data);
    const { uid } = data;
    videoChatStreamUrl.pullUrlMap[uid].streamType.audio = true;
  };

  const pusherStateChange=(target)=> {
    client.value.reportPusherStateChange(target.detail.code, target.detail.message);
  }

  const pusherNetStatusChange =(e)=> {
    const info = e?.detail?.info;
    client.value.reportPusherNetStatusChange(info);
  }

  const playerStateChange = (e) => {
    const uid = e?.uid;
    const screen = e?.screen;

    client.value.reportPlayerStateChange(
      uid,
      e?.e?.detail?.code,
      e?.e?.detail?.message,
      screen
    );
  }

  const playerNetStatusChange=(e)=> {
    const uid = e?.uid;
    const screen = e?.screen;
    const info = e?.e?.detail?.info;
    client.value.reportPlayerNetStatusChange(uid, info, screen);
  }

  /** 事件 */
  const LiveEventMap = {
    /** 远端用户进入房间 */
    [EVENTS.PEER_ONLINE]: onPeerOnline,
    /** 远端用户离开房间 */
    [EVENTS.PEER_LEAVE]: onPeerLeave,
    /** 错误事件 */
    [EVENTS.ERROR]: liveError,
    /** 远端用户发布音视频流 */
    [EVENTS.STREAM_ADDED]: onPeerPublish,
    /** 远端用户停止发布 */
    [EVENTS.STREAM_REMOVED]: onPeerUnpublish,
    /** 推流或拉流地址更新 */
    [EVENTS.STREAM_UPDATED]: onPeerStreamUpdated,
    /** 推流或拉流异常 */
    [EVENTS.STREAM_ERROR]: onPeerStreamError,
    /** 异地登录被挤出 */
    [EVENTS.CLIENT_BANNED]: onClientBanned,
    /** 远端关闭音频 */
    [EVENTS.MUTE_AUDIO]: onPeerMuteAudio,
    /** 远端开启音频 */
    [EVENTS.UNMUTE_AUDIO]: onPeerUnmuteAudio,
  };

  /** 开启监听 */
  const addEventListener = () => {
    Object.keys(LiveEventMap).forEach((event) => {
      client.value.on(event, LiveEventMap[event]);
    });
  };

  /** 关闭监听 */
  const removeEventListener = () => {
    Object.keys(LiveEventMap).forEach((event) => {
      client.value.off(event, LiveEventMap[event]);
    });
  };

  return {
    videoChatStreamUrl,
    init,
    getCurrentVideoMode,
    mediaState,
    handleSwitchCamera,
    handleMute,
    handleSpeaker,
    handleHangUp,
    pusherStateChange,
    pusherNetStatusChange,
    playerStateChange,
    playerNetStatusChange,
    publish,
    unpublish,
    subscribe
  };
}
