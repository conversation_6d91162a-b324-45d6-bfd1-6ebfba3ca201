<template>
  <view
      class="medicine-class"
      v-for="(item,index) in props.Pharmaceutical_information_list"
      :key="index"
  >
    <view class="medicine-message">
      <view class="medicine-message-top">
        <img :src="medicineIcon" alt="" class="medicine-icon">
        <view class="medicine-message-top-l">
          <text class="medicine-name">{{ item.productName}}</text>
          <text class="medicine-specification">
            <text  v-if="prescriptionDrugType == PrescriptionDrugType.ChineseMedicine" >{{item.count}}</text>
            {{item.specName}}
          </text>
        </view>
        <view class="medicine-message-top-r" v-if="prescriptionDrugType != PrescriptionDrugType.ChineseMedicine" >
          <text class="medicine-measurement">
            X{{item.count}}
          </text>
        </view>
      </view>
      <view class="medicine-message-bottom"  v-if="prescriptionDrugType != PrescriptionDrugType.ChineseMedicine" >
        <view v-if="index !== props.Pharmaceutical_information_list.length - 1" class="medicine-message-dashed-line">
        </view>
        <view class="medicine-dose-frequency" :style="{'margin-left': index !== props.Pharmaceutical_information_list.length - 1?'0':'40rpx'}">
          {{ getRouteProperty(item,'routeOfAdministration',
            'route')?routeOfAdministration_list[getRouteProperty(item,'routeOfAdministration',
            'route')]:getRouteProperty(item,'routeOfAdministrationOther',
            'custRoute') }}
          &nbsp;
          {{ getRouteProperty(item,'frequencyOfAdministration',
            'freq')?frequencyOfAdministration_list[getRouteProperty(item,'frequencyOfAdministration',
            'freq')]:getRouteProperty(item,'frequencyOfAdministrationOther',
            'custFreq') }}
          &nbsp;
          每次{{item.dosage}} {{dosageUnits_list[getRouteProperty(item,'dosage',
            'units')-1]}}
        </view>
      </view>
    </view>
  </view>
  <!-- 中药处方信息 -->
  <view class="chinese-medicine-message" v-if="prescriptionDrugType == PrescriptionDrugType.ChineseMedicine" >
    <view class="omit" v-if="rpSize && rpSize > 2" >. . .</view>
    共{{ Pharmaceutical_information_list[0].chineseDosage || 0 }}剂  {{ getRouteProperty(Pharmaceutical_information_list[0],'routeOfAdministration',
            'route')?routeOfAdministration_list[getRouteProperty([0],'routeOfAdministration',
            'route')]:getRouteProperty(Pharmaceutical_information_list[0],'routeOfAdministrationOther',
            'custRoute') }}

            {{ getRouteProperty(Pharmaceutical_information_list[0],'frequencyOfAdministration',
            'freq')?frequencyOfAdministration_list[getRouteProperty(Pharmaceutical_information_list[0],'frequencyOfAdministration',
            'freq')]:getRouteProperty(Pharmaceutical_information_list[0],'frequencyOfAdministrationOther',
            'custFreq') }}

            每次{{Pharmaceutical_information_list[0].dosage}}{{dosageUnits_list[getRouteProperty(Pharmaceutical_information_list[0],'units',
            'dosageUnits')-1]}}

  </view>
</template>

<script setup lang="ts">
import { computed,ref } from "vue";
import medicineIcon from "@/static/images/user/medicalConsultation/medicineIcon.png"
import type {presRpListDTO} from "@/components/PharmaceuticalInformation/hook/type";
import pharmaceuticalInformation from "@/components/PharmaceuticalInformation/hook/pharmaceuticalInformation";
import { PrescriptionDrugType } from "@/enum/prescriptionsEnum";
const {routeOfAdministration_list, frequencyOfAdministration_list,dosageUnits_list,getRouteProperty} = pharmaceuticalInformation()
interface Props {
  Pharmaceutical_information_list:presRpListDTO[];
  /** 处方单药品类型 */
  prescriptionDrugType:PrescriptionDrugType;
  /** 原RP取药记录数 */
  rpSize?:number;
}
const props = withDefaults(defineProps<Props>(),{
  Pharmaceutical_information_list: () => {
    return []
  },
})
</script>

<style scoped lang="scss">
.medicine-class{
  margin-top: 10rpx;
  .medicine-message {
    .medicine-message-top{
      display: flex;
      .medicine-icon{
        width: 24rpx;
        height: 24rpx;
        margin: 8rpx 16rpx 8rpx 0;
      }
      .medicine-message-top-l{
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .medicine-name{
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 16rpx;
        }
        .medicine-specification{
          font-weight: 400;
          font-size: 24rpx;
          color: #333333;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin: 4rpx 0;
        }
      }
      .medicine-message-top-r{
        .medicine-measurement{
          font-weight: 400;
          font-size: 28rpx;
          color: #666666;
          line-height: 40rpx;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .medicine-message-bottom{
      display: flex;
      .medicine-message-dashed-line{
        height: 28rpx;
        border: 2rpx dashed rgba(0,0,0,0.25);
        margin: 4rpx 27rpx 4rpx 11rpx;
      }
      .medicine-dose-frequency{
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 32rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 4rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.chinese-medicine-message{
  .omit{
    color: #000000;
  }
  padding-left: 40rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  color: #999999;
}
</style>