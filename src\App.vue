<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { accountLogin, getStateInfo } from "./services/api/account";
import { bindGroupManager } from "./services/api/placeOrder";
import { getDisplayStatue , getParamByKey,getGlobalConfigs } from "./services/api/system";
import { getLoginCode } from "./utils/wxSdkUtils/account";
import { navigateTo } from "./routes/utils/navigateUtils";
import { routesMap } from "@/routes/maps";
import { RouteName } from "./routes/enums/routeNameEnum";
import { userLogin } from "./utils/accountUtils";
import { isObject,isString,isEmpty } from "./utils/isUtils";
import { parseQueryString } from "./utils/urlUtils";
import { useSystemStoreWithoutSetup } from "./stores/modules/system";
import { useUserInfoStoreWithoutSetup } from "./stores/modules/user";
import { useDistributionStoreWithoutSetup } from "./stores/modules/distribution";
import { SystemParamKeyEnum } from "@/enum/systemConfigEnum"
import { StoreNamesEnum,StoreQualityEnum , StorePayMode ,DistributionScene } from "@/enum/storeNamesEnum";
import {UserPayment} from "@/hooks/payment"
import { useRoute } from "./hooks/useRoute";
import { useTabbar } from "@/components/Tabbar/hooks/useTabbar";
const {navigationListRef, loadNavigationConfig } = useTabbar()
const { getPayStatusFn } = UserPayment();
/** 社群相关 */
import { getActualState, getAuditMode, getOfficialState, isAllowDrainage, miniAppLogin } from "./services/api/S";
import { useOpenLoginDialog } from "@/components/S/OpenLoginDialog/hooks/useOpenLoginDialog";
import { createCacheStorage } from "./utils/S/cache/storage";
import { CacheConfig } from "./utils/S/cache/config";
import { afterLogin, miniAppLoginFn } from "./utils/S/accountUtils";
import { useUserStoreWithoutSetup } from "./stores/S/user";
const sgUserStore = useUserStoreWithoutSetup();

let isAlreadyLaunch = false
const checkTimeInfo = {
  timestamp:void 0,
  isUse:false,
}
const userStore = useUserInfoStoreWithoutSetup();
const systemStore = useSystemStoreWithoutSetup();
const distributionStore = useDistributionStoreWithoutSetup();
const {init} = useRoute()
onLaunch(async(options) => {
  console.log("App Launch");
  bootstrap()
  try{
    await checkState()
    // await checkMallLink()
  }
  catch(e){
    console.log('launch check error',e);
  }
  isAlreadyLaunch = true
});

onShow(async(options) => {
  const {changeOpenLoginDialogShow} = useOpenLoginDialog()
  changeOpenLoginDialogShow(false)
  wx.hideTabBar({})
  init()
  console.log(options,'APPonShow-验证H5开放标签跳转功能-extra-data参数');
  
  const payUrl = routesMap[RouteName.Pay].path
  if (options.path == payUrl && systemStore.payMode == StorePayMode.fuiouPay ) {
    if (options.referrerInfo?.extraData?.code != "cancel" && options.referrerInfo?.extraData?.isLiveStream != 1 ) {
      getPayStatusFn(options.query.orderCode)
    }
  }
  // 扫描小程序码进入
  const codeScenes = [1047,1048,1049]
  /** 小程序课程state */
  // let miniProgramState = "";
  if ( codeScenes.includes(options.scene) && isString(options.query.scene) ) {
    // sg_对应社群业务
    if (options.query.scene.substring(0, 3) == "sg_") {
      /** 去掉前三位 */
      // miniProgramState = options.query.scene;
      sgLogin(options.query.scene)
    }else{
      const query = parseQueryString(decodeURIComponent(options.query.scene)) 
      if (query.a == DistributionScene.isDistribution) {
        distributionStore.setDistributionScene(true);
        distributionStore.setDistributorId(query.b);
        navigationListRef.value = [];
        loadNavigationConfig()
      }
    }
  }else if (options.scene == 1194 || options.scene == 1167) {
    // miniProgramState = options.query.state;
    sgLogin(options.query.state)
  }
  console.log("App Show");
  if(isAlreadyLaunch){
    try{
      bootstrap()
      await checkState()
      // await checkMallLink()
    }
    catch(e){
      console.log('show check error',e);
  }
  }
  
});

const sgLogin = async(miniProgramState:string)=>{
  console.log('sgLogin',miniProgramState);
  
  try{
    if(!miniProgramState){
      throw new Error('no state')
    }
    else{
      const SaStorage = createCacheStorage(CacheConfig.SApi)
      const splitArray = miniProgramState.split('_')
      if(splitArray[1]){
        SaStorage.set(splitArray[1])
      }
    }
    let isAuditMode = true
    isAuditMode = await getAuditMode()
    if(isAuditMode == 'true'){
      throw new Error('madeFalse')
    }
    else{
      try{
        uni.showToast({
          title: "正在处理中...",
          icon: "none",
        });
        const totalState = await convertActualState(miniProgramState)
        console.log(totalState,'totalState');
        
        if(totalState.mixLogin){
          navigateTo({
            url: RouteName.SWebView,
          });
          return
        }
        else{
          try{
            const {changeOpenLoginDialogShow} = useOpenLoginDialog()
            const loginCode = await getLoginCode()
            const params = {
              code: loginCode,
              miniProgramState: miniProgramState
            }
            const resp = await miniAppLogin(params)
            afterLogin(resp)
            let isAllowDrainageFlag = false
            try{
              const resp = await isAllowDrainage()
              isAllowDrainageFlag = resp === 'true'?true:false
            }
            catch(e){
              isAllowDrainageFlag = false
            }
            if ((resp.dealerId && resp.groupMgrId && resp.groupMgrId !=='1000') || isAllowDrainageFlag) {
              // changeOpenLoginDialogShow(true)
              navigateTo({
                url: RouteName.Demo,
              });
            }
          }
          catch(err){
            try{
              if (err.message.includes('madeFalse')) {
                return
              }
              uni.showToast({
                title: `登录失败:${err}`,
                icon: "none",
              });
              console.log(err, "登录失败");
            }
            catch(e){}
          }
        }
      }
      catch(e){}


      // let isAllowDrainageFlag = false
      // try {
      //   const resp = await isAllowDrainage()
      //   isAllowDrainageFlag = resp === 'true' ? true : false
      // }
      // catch (e) {
      //   isAllowDrainageFlag = false
      // }
      // try {
      //   // navigateTo({
      //   //   url: RouteName.SWebView,
      //   // });
      //   const res = await miniAppLoginFn(miniProgramState)
      //   if (res.type == userTypeEnum.MEMBER) {
      //     if ((res.dealerId && res.groupMgrId && res.groupMgrId !== '1000') || isAllowDrainageFlag) {
      //       convertActualState(miniProgramState);
      //     }
      //   }
      //   else {
      //     convertActualState(miniProgramState);
      //   }
      // }
      // catch (err) {
      //   try {
      //     if (err.message.includes('madeFalse')) {
      //       return
      //     }
      //     uni.showToast({
      //       title: `登录失败:${err}`,
      //       icon: "none",
      //     });
      //     console.log(err, "登录失败");
      //   }
      //   catch (e) {}
      // }
    }
  }
  catch(e){}
}

async function convertActualState(miniProgramState: string) {
  try{
    const actualStateResp = await getActualState(miniProgramState)
    if(actualStateResp.state == 'null'){
      throw new Error('state is null')
    }
    try{
      const stateResp = await getOfficialState(actualStateResp.state) 
      const {
          id,
          wxappid,
          wxappImg,
          name,
          corpId,
          agentId,
          qwId,
          gmName,
          courseDto,
          dealerId,
          gmId,
          gmImg,
          isShowMgrInfo,
        } = stateResp;
        const totalState = {
          id,
          appId: wxappid.trim(),
          wxappImg,
          name,
          corpId,
          agentId,
          qwId,
          gmName,
          courseDto,
          dealerId,
          gmId,
          gmImg,
          isShowMgrInfo,
          ...actualStateResp
        }
        sgUserStore.setOfficialState(totalState);
        sgUserStore.setCourseState(actualStateResp.state);
        return totalState
    }
    catch(e){
      uni.showToast({
        title: e || "转换实际state失败",
        icon: "none",
      });
      throw new Error('get official state error')
    }
  }
  catch(e){
    uni.showToast({
      title: e || "get state error",
        icon: "none",
    });
    throw new Error('get state error')
  }
}

onHide(() => {
  console.log("App Hide");
});

async function jumpToGoodsDetailByState(state:string){
  try{
    const { productId, hasMobile } = await getStateInfo(state)
    if(!productId){
      return 
    }
    else{
      if (hasMobile) {
        const _loginCode = await getLoginCode()
        try {
          const userInfo = await accountLogin({code:_loginCode})
          userStore.setUnionId(userInfo.encryptUnionId)
          userStore.setUserInfo(userInfo)
	        userStore.setToken(userInfo.token)
        }
        catch (e) {
          console.log('login error:', e);
        }
      }
      navigateTo({
        url:RouteName.GoodsDetail,
        props:{
          id:productId
        }
      })
      return
    }
  }
  catch(e){
    console.log('get state error:',e);
    return
  }
}

async function checkState() {
  let _state:string
  let _type = null
  const options = uni.getEnterOptionsSync()
  // console.log(options,'options');
  const _extraData = options.referrerInfo.extraData || {}
  const extraData = isObject(_extraData)?_extraData:JSON.parse(_extraData)
  //记录时间戳
  if(extraData.timestamp == checkTimeInfo.timestamp){
    console.log('check time info is already use');
    return
  }
  // systemStore.setNavigationConfigList([])
  checkTimeInfo.timestamp = extraData.timestamp
  if(options.scene == 1167){
    if(extraData.wxEntityId){
      userStore.setDefaultChannelId(extraData.wxEntityId)
    }
    else{
      _state = extraData.state ?? extraData.mallLink
      _type = extraData.mallLink
    }
  }
  else{
    _state = options.query.scene
  } 
  if(_state){
    try{
      if(_type){
        await jumpToMallLink(_state)
      }else{
        await jumpToGoodsDetailByState(_state)
      }
    }
    catch(e){
      console.log('get state error:',e);
    }
  }
}
// async function checkMallLink() {
//   let mallLink:string
//   const options = wx.getLaunchOptionsSync()
//   if(options.scene == 1167){
//     const _extraData = options.referrerInfo.extraData || {}
//     const extraData = isObject(_extraData)?_extraData:JSON.parse(_extraData)
//     mallLink = extraData.mallLink
//   }
//   else{
//     mallLink = options.query.scene
//   }
//   if(mallLink){
//     try{
//       await jumpToMallLink(mallLink)
//     }
//     catch(e){
//       console.log('get state error:',e);
//     }
//   }
// }
async function jumpToMallLink(mallLink:string) {
  try{
    await bindGroupManager(mallLink)
    const _loginCode = await getLoginCode()
      try {
        const userInfo = await accountLogin({code:_loginCode})
        userStore.setUserInfo(userInfo)
	      userStore.setToken(userInfo.token)
        // navigateTo({url:RouteName.OrderAgent})
      }
      catch (e) {
        console.log('login error:', e);
      }
    return
  }
  catch(e){
    console.log('get state error:',e);
    return
  }
}
async function bootstrap() {
  getGlobalConfigsFn()
}
// // 是否限制显示健康板块内容
// async function getDisplayStatueFn() {
//   try {
//     const res = await getDisplayStatue({version:systemStore.version})
//     systemStore.setLimit(res)
//   } catch (error) {
//     console.log('getDisplayStatue error:',error)
//   }
// }
// // 是否开启积分商城
// function getParamByKeyFn (){
//   getParamByKey(SystemParamKeyEnum.IntegralStore).then(res=>{
//     systemStore.setIsShowIntegralStore(!!res.value)
//   }).catch(err=>{
//     console.log('getParamByKey error:',err)
//   })
// }

/**获取系统全局配置 */
async function getGlobalConfigsFn() {
  try {
    const res = await getGlobalConfigs({version:systemStore.version})
    systemStore.setLimit(res.isLimit)
    systemStore.setIsShowIntegralStore(res.isShowIntegralstore)
    systemStore.setPayMode(res.payMode)
    systemStore.setFyPayMode(res.fyPayMode)
    systemStore.setStoreType(res.marketplaceType)
    systemStore.setIsUsingDistribution(res.isUsingDistribution)
    systemStore.setInstitutionName(res.institutionName)
    systemStore.setImSdkAppId(res.imSdkAppId)
    systemStore.setIsAudit(res.isAudit || false)
    systemStore.setStoCsDrugUserIdnoRadio(res.isAddIdNo??true)
  } catch (error) {
    console.log('getGlobalConfigs error:',error)
  }
}

</script>
<style lang="scss" >
 @import '/src/wxcomponents/vant/common/index.wxss';
 @import '/src/style/vantVar.wxss';
 @import "/src/static/css/common.scss";

</style>
