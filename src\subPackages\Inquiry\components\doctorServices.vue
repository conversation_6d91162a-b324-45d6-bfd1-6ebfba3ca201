<template>
  <view>
        <view
          class="doctor-card-footer-inquiry doctor-card-footer-inquiry-imgText"
          v-if="doctorDetailData.isPictureText == 1"
        >
          <view class="doctor-card-footer-left">
            <image
              :src="imgText"
              mode="scaleToFill"
              class="doctor-card-footer-left-image"
            />
          </view>
          <view class="doctor-card-footer-right">
            <view class="doctor-card-footer-right-handle">
              <view class="doctor-card-footer-right-introduce">
                <view class="doctor-card-footer-right-title"> 图文问诊 </view>
                <view class="doctor-card-footer-right-price">
                  <span
                    class="price-number"
                    v-if="doctorDetailData.consultationFee > 0"
                    >￥{{
                      (doctorDetailData.consultationFee / 100).toFixed(2)
                    }}</span
                  >
                  <span class="price-number" v-else>免费</span>
                  <span> / {{ formatTime(doctorDetailData.pictureDuration) }} </span>
                </view>
              </view>
              <view @click="handleConsult(InquiryTypeEnum.PictureText)">
                <van-button
                  size="small"
                  color="#1677FF"
                  :disabled="!getConsultationStatus(doctorDetailData)"
                  round
                  type="info"
                >
                  去咨询
                </van-button>
              </view>
            </view>
            <view class="doctor-card-footer-right-describe">
              · 通过图文方式与医生沟通，不限次数
            </view>
            <view class="doctor-card-footer-right-describe">
              · 若在规定时间段内未接诊，自动退款
            </view>
          </view>
        </view>

        <view
          class="doctor-card-footer-inquiry doctor-card-footer-inquiry-video"
          v-if="doctorDetailData.isVideo == 1"
        >
          <view class="doctor-card-footer-left">
            <image
              :src="videoImg"
              mode="scaleToFill"
              class="doctor-card-footer-left-image"
            />
          </view>
          <view class="doctor-card-footer-right">
            <view class="doctor-card-footer-right-handle">
              <view class="doctor-card-footer-right-introduce">
                <view class="doctor-card-footer-right-title"> 视频问诊 </view>
                <view class="doctor-card-footer-right-price">
                  <span
                    class="price-number"
                    v-if="doctorDetailData.videoConsultationFee > 0"
                    >￥{{
                      (doctorDetailData.videoConsultationFee / 100).toFixed(2)
                    }}</span
                  >
                  <span class="price-number" v-else>免费</span>
                  <span> / {{ formatTime(doctorDetailData.videoDuration) }} </span>
                </view>
              </view>
              <view @click="handleConsult(InquiryTypeEnum.Video)">
                <van-button
                  size="small"
                  color="#4BE092"
                  round
                  type="info"
                  :disabled="!getConsultationStatus(doctorDetailData)"
                >
                  去咨询
                </van-button>
              </view>
            </view>
            <view class="doctor-card-footer-right-describe">
              · 通过视频通话方式与医生沟通，不限次数
            </view>
            <view class="doctor-card-footer-right-describe">
              · 若在规定时间段内未接诊，自动退款
            </view>
          </view>
        </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted} from 'vue'
import { InquiryTypeEnum, type DoctorEntityPageDTO } from '../type'
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserInfoStoreWithoutSetup();
import {  navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { useDoctor } from "@/subPackages/Inquiry/hooks/useDoctor";
const { getConsultationStatus, getJumpFlagForCounselingFn } = useDoctor();
import { formatTime } from "@/subPackages/Inquiry/utils/timeUtils";
import imgText from "@/static/images/inquiry/imgText.png";
import videoImg from "@/static/images/inquiry/videoInquiry.png";

const props = defineProps<{
  doctorDetailData: DoctorEntityPageDTO
}>();


const handleConsult = (type: InquiryTypeEnum) => {
  if (
    (type === InquiryTypeEnum.PictureText && props.doctorDetailData.isPictureText == 0) ||
    (type === InquiryTypeEnum.Video && props.doctorDetailData.isVideo == 0) ||
    props.doctorDetailData.onlineStatus == 0
  ) {
    uni.showToast({
      title: `医生${
        props.doctorDetailData.isPictureText == 0
          ? "暂未提供此项服务"
          : "休息中"
      }`,
      icon: "none",
    });
    return;
  } else {
    if (!userStore.token) {
      navigateTo({
        url: RouteName.Login,
      });
      return;
    }
    getJumpFlagForCounselingFn(props.doctorDetailData, type);
  }
};

</script>
<style scoped lang="scss">
@import "@/subPackages/Inquiry/css/doctorStyle.scss";

</style>