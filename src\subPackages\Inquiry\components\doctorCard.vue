<template>
  <view class="doctor-card" @click="handleDoctorDetail">
    <view class="doctor-card-header">
      <view
        class="doctor-card-avatar"
        :class="{ 'doctor-card-avatar-online-border': item.onlineStatus == 1 }"
      >
        <image
          :src="item.img ? item.img : defaultAvatar"
          class="doctor-card-avatar-image"
          mode="scaleToFill"
          @error="handleAvatarError"
        />
        <view class="doctor-card-avatar-online" v-if="item.onlineStatus == 1"
          >在线</view
        >
      </view>
      <view class="doctor-card-info">
        <view class="doctor-card-info-user">
          <view class="doctor-card-info-user-name">{{ item.doctorName }}</view>
          <view class="doctor-card-info-user-title">{{
            doctorTitleMap[item.title]
          }}</view>
          <view class="doctor-card-info-user-department">{{
            item.departmentName
          }}</view>
        </view>
        <view class="doctor-card-hospital">{{ item.institutionName }}</view>
        <view class="doctor-card-specialize" v-if="item.beGoodAt">
          <text class="doctor-card-specialize-title">擅长：</text>
          <text class="doctor-card-specialize-content">{{
            item.beGoodAt
          }}</text>
        </view>
      </view>
    </view>
    <view class="doctor-card-footer">
      <van-divider custom-style="margin:30rpx 0rpx" />
      <!-- 图文咨询 -->
      <view class="doctor-card-consult">
        <view class="doctor-card-consult-price-container" v-if="item.isPictureText == 1 || item.isVideo == 1">
          <view class="doctor-card-consult-price" v-if="item.isPictureText == 1">
            <span class="doctor-card-consult-price-title">图文：</span>
            <span
              class="doctor-card-consult-price-number"
              v-if="item.consultationFee > 0"
              >￥{{ (item.consultationFee / 100).toFixed(2) }}</span>
            <span class="doctor-card-consult-price-number" v-else>免费</span>
          </view>
          <view class="doctor-card-consult-price" v-if="item.isVideo == 1">
            <span class="doctor-card-consult-price-title">视频：</span>
            <span
              class="doctor-card-consult-price-number"
              v-if="item.videoConsultationFee > 0"
              >￥{{ (item.videoConsultationFee / 100).toFixed(2) }}</span
            >
            <span class="doctor-card-consult-price-number" v-else>免费</span>
          </view>
        </view>
        <view class="doctor-card-consult-nothing" v-else>暂未提供服务</view>
        <view
          class="doctor-card-consult-button"
          :class="{
            'doctor-card-consult-button-disabled': !getConsultationStatus(item),
          }"
          @click.stop="handleConsult"
          >咨询医生</view
        >
      </view>
    </view>
    <DoctorServiceModal v-model:show="isShowServiceModal" :doctorDetailData="item" />
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { type DoctorEntityPageDTO, doctorTitleMap, InquiryTypeEnum } from "../type";
import defaultAvatar from "@/static/images/user/defaultAvatar.jpg";
import { useUserInfoStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserInfoStoreWithoutSetup();
import { navigateTo } from "@/routes/utils/navigateUtils";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { useDoctor } from "@/subPackages/Inquiry/hooks/useDoctor";
const { getConsultationStatus, getJumpFlagForCounselingFn } = useDoctor();
import DoctorServiceModal from "./doctorServicemodal.vue";

const props = defineProps<{
  item: DoctorEntityPageDTO;
}>();

const handleAvatarError = () => {
  props.item.img = defaultAvatar;
};

const isShowServiceModal = ref(false);

const handleConsult = () => {
  if ((props.item.isPictureText == 0 && props.item.isVideo == 0 ) || props.item.onlineStatus == 0) {
    uni.showToast({
      title: `医生${props.item.onlineStatus == 0 ? "休息中" : "暂未提供服务"}`,
      icon: "none",
    });
    return;
  }
  if (!userStore.token) return navigateTo({url: RouteName.Login});
  // 图文问诊和视频问诊都开启的弹窗选择
  if (props.item.isPictureText == 1 && props.item.isVideo == 1) return isShowServiceModal.value = true;
  getJumpFlagForCounselingFn(props.item, props.item.isPictureText == 1 ? InquiryTypeEnum.PictureText : InquiryTypeEnum.Video );
  
};

const handleDoctorDetail = () => {
  navigateTo({
    url: RouteName.InquiryDoctorDetail,
    props: {
      doctorId: props.item.id,
    },
  });
};
</script>

<style lang="scss" scoped>
@import "../css/doctorStyle.scss";
.doctor-card {
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: #fff;
  margin-bottom: 16rpx;

  /* doctor-card-info 元素补满剩余空间 */
  .doctor-card-header {
    display: flex;
    gap: 32rpx;
  }
  .doctor-card-info {
    flex: 1;
  }
  .doctor-card-footer {
    .doctor-card-consult {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .doctor-card-consult-price-container {
        display: flex;
        align-items: center;
        gap: 32rpx;
        .doctor-card-consult-price-title {
          font-size: 28rpx;
          color: #333333;
        }
        .doctor-card-consult-price-number {
          font-size: 28rpx;
          color: #1677ff;
        }
      }
      .doctor-card-consult-button {
        width: 152rpx;
        height: 56rpx;
        background: #f3f3f3;
        border-radius: 56rpx;
        text-align: center;
        line-height: 56rpx;
        font-size: 28rpx;
        color: #1677ff;
        font-weight: bold;
      }
      .doctor-card-consult-button-disabled {
        background: #f3f3f3;
        color: #999999;
      }
    }
  }
}
</style>
