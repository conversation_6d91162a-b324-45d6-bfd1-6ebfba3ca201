import { defineStore } from "pinia";
import { stores } from "@/stores";
import { StoreNamesEnum,StoreQualityEnum , StorePayMode , StoreFyPayMode,StoreTypeEnum , NowOrderType } from "@/enum/storeNamesEnum";
import { createCacheStorage } from "@/utils/cache/storage";
import { CacheConfig } from "@/utils/cache/config";
export interface SystemInterface{
    /** 版本 */
    version:string,
    /** 是否限制显示健康板块内容 */
    isLimit:boolean,
    /** 是否开启积分商城 */
    isShowIntegralStore:boolean,
    /**商城性质 */
    storeQuality:StoreQualityEnum.public,
    /** 导航配置 */ 
    navigationConfigList?:[],
    /** 支付方式  */
    payMode:StorePayMode,
    /** 富友支付方式 */
    fyPayMode:StoreFyPayMode,
    /** 商城类型 */
    storeType:number,
    /**当前订单类型 */
    nowOrderType:NowOrderType,
    /** 是否开启分销功能 */
    isUsingDistribution:boolean
    /** 被邀请key(点击分销员链接获取的)  */
    sharingInfoKey:string
    /** 当前用户邀请key */
    inviteKey?:string
    /** 医疗机构名称 */
    institutionName?:string
    /** imSdkAppId */
    imSdkAppId?:number
    /** 是否审核模式 */
    isAudit:boolean,
    /** 是否必填身份证 */
    isAddIdNo?:boolean,
}
export const systemStore = defineStore(StoreNamesEnum.system, {
    state: ():SystemInterface => {
      return {
        version:'1.0.4',
        isLimit: true,
        isShowIntegralStore:true,
        storeQuality:StoreQualityEnum.public,
        payMode:StorePayMode.weChatPay,
        fyPayMode:StoreFyPayMode.currentApp,
        storeType:StoreTypeEnum.medicine,
        nowOrderType:NowOrderType.StoreOrder,
        isUsingDistribution:false,
        sharingInfoKey:'',
        inviteKey:'',
        institutionName:'',
        imSdkAppId:0,
        isAudit:false,
        isAddIdNo:true,
      };
    },
    actions: {
      setLimit(limit: boolean) {
        this.isLimit = limit;
      },
      setVersion(version: string) {
        this.version = version;
      },
      setIsShowIntegralStore(value:boolean){
        this.isShowIntegralStore = value;
      },
      setStoreQuality(quality: number) {
        this.storeQuality = quality;
      },
      setNavigationConfigList(config){
        this.navigationConfigList = config
      },
      setPayMode(type){
        this.payMode = type
      },
      setFyPayMode(type){
        this.fyPayMode = type
      },
      setStoreType(type){
        this.storeType = type
      },
      setNowOrderType(type){
        this.nowOrderType = type
      },
      setSharingInfoKey(key:string){
        this.sharingInfoKey = key;
        const authStorage = createCacheStorage(CacheConfig.sharingInfoKey);
        authStorage.set(key);
      },
      setInviteKey(key:string){
        this.inviteKey = key;
      },
      setIsUsingDistribution(value:boolean){
        this.isUsingDistribution = value
      },
      setInstitutionName(name:string){
        this.institutionName = name
      },
      setImSdkAppId(appId:number){
        this.imSdkAppId = appId
      },
      setIsAudit(value:boolean){
        this.isAudit = value
      },
      setStoCsDrugUserIdnoRadio(isAddIdNo:boolean){
        this.isAddIdNo = isAddIdNo
      },
    },
    getters:{
      getIsShowIntegralStore:state=>{
        return state.isShowIntegralStore
      },
      getStoreType:(state):boolean=>{
        if(state.storeType == StoreTypeEnum.medicine&&state.isLimit){
          return true
        }
        return false
      },
      getInstitutionName:(state):string=>{
        return state.institutionName
      },
      getImSdkAppId:(state):number=>{
        return state.imSdkAppId
      },
      getIsAudit:(state):boolean=>{
        return state.isAudit
      }
    }
  });
  
  export function useSystemStoreWithoutSetup() {
    return systemStore(stores);
  }
  