<template>
    <view class="verify">
        <view class="form">
            <van-field @change="onChange('name', $event)" label="收件人" title-width="150rpx" required :border="false"
                :value="form.name" :error-message="errors.name">
                <input type="text" slot="input" v-model="form.name" placeholder="输入姓名">
            </van-field>
            <van-field :border="false" :value="form.mobile" label="手机号码" title-width="150rpx" required placeholder="输入号码"
                :error-message="errors.mobile"  >
                <view slot="input" class="phoneInput">
                    <view>+86</view>
                    <input type="number" placeholder="输入号码" v-model="form.mobile" @input="mobileBlur" maxlength="11" >
                </view>
            </van-field>
            <van-field :border="false" label="所在地区" title-width="150rpx" required @click-input="showPopupFn" @click-icon="showPopupFn" >
                <view slot="right-icon"><van-icon name="arrow" /></view>
                <view slot="input" class="selectAddress">
                    <input type="text" disabled="false" v-model="region" placeholder="省、市、区、街道">
                </view>
            </van-field>
            <van-field class="detailAddress" @change="addressChangeFn" :border="false" label="详细地址" required
                title-width="150rpx" :value="form.address" type="textarea" :autosize="{ minHeight: 82 }" placeholder="输入地址"
                :error-message="errors.address">
            </van-field>
            <view class="defaultSet" v-if="placeOrder != '1'" >
                <view>设为默认地址</view>
                <van-switch :checked="form.isDefault === 1" @change="defaultSetFn" size="24px" active-color="#4DA4FF" />
            </view>
        </view>

        <view class="footerBtn">
            <buttonGroup title="保存" @cancel="onCancel" @confirm="confirm" />
        </view>

        <van-popup :show="popupShow" round closeable close-icon="close" position="bottom" @close="popupClose">
            <view class="popup-box">
                <view class="popup-heard">
                    <view class="heard-title">选择所在地区</view>
                    <view class="popup-steps">
                        <van-steps v-if="stepsShow" :steps="steps" :active="steps.length - 1 > 1 ? steps.length - 1 : 1"
                            active-icon="circle" direction="vertical" active-color="#4DA4FF" @click-step="clickStep" />
                    </view>
                </view>
                <view class="popup-content">
                    <view class="popup-selectAddress">
                        <view class="selectAddress-title">选择地址</view>
                        <view class="selectAddress-content" v-for="(classPy, index) in addressJson" :key="index">
                            <view class="content-flex">
                                <view class="address-py">{{ classPy.letter }}</view>
                                <view class="address-radio-group">
                                    <view class="address-radio" @click="radioClick(item)"
                                        :class="item.id === activeId ? 'active' : ''"
                                        v-for="(item, index) in classPy.items" :key="index"> {{ item.name }}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="popup-footer">
                    <view class="leftBtn">
                        <van-button color="#F8F8F8" @click="resetFn" round type="default">重置</van-button>
                    </view>
                    <view class="rightBtn">
                        <van-button type="primary" @click="confirmAddress" :disabled="disabled" round block>确定</van-button>
                    </view>
                </view>
            </view>
        </van-popup>
    </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import buttonGroup from "@/components/buttonGroup/index.vue"
import { onLoad, onShow } from '@dcloudio/uni-app';
import { AddressTypeEnum } from '@/enum/userTypeEnum'
import { isEmpty } from '@/utils/isUtils'
import { addAddress, editAddress, addressData } from '@/services/api/user';
import { addPlaceOrderAddress , editPlaceOrderAddress } from '@/services/api/placeOrder';
import { userClassPinyin } from '../hooks';
import { navigateBack } from '@/routes/utils/navigateUtils';
const type = ref('')    // 新增or编辑
const placeOrder = ref(null) // 是否代下单
onLoad((e) => {
    type.value = e.type
    placeOrder.value = e.placeOrder
    if (e.type == AddressTypeEnum.edit) {
        uni.setNavigationBarTitle({
            title: '编辑收货地址'
        })
        const data = JSON.parse(e.data)
        delete data.createTime
        delete data.updateTime
        Object.assign(form, data)
    } else {
        uni.setNavigationBarTitle({
            title: '新增收货地址'
        })
    }
})

const textMap: string[] = ['选择省份', '选择城市', '选择区县', '选择街道']

const mobileBlur = (e: any) => {
    if (isEmpty(e.detail.value)) {
        errors.mobile = '手机号码不能为空'
    } else if (!/^1[3456789]\d{9}$/.test(e.detail.value)) {
        errors.mobile = '手机号码格式不正确'
    } else {
        errors.mobile = ''
    }
}


// 进入页面获取省级地址数据
onShow(() => {
    getAddressData("0",1)
})

// 地址信息
const form = reactive({
    "name": "",
    "mobile": "",
    "companyId": "",
    "company": "",
    "provinceId": "",
    "province": "",
    "cityId": "",
    "cityName": "",
    "areaId": "",
    "area": "",
    "townId": "",
    "town": "",
    "address": "",
    "isDefault": 1,
    "id": ""
})


const addressChangeFn = (e) => {
    form.address = e.detail
}

const region = computed(() => {
    return form.province + form.cityName + form.area + form.town
})

// 获取级联数据
const addressJson = ref([])
// 获取地址数据
const getAddressData = (code:string,cateType:number) => {
    
    addressJson.value = []
    addressData({ code , cateType }).then((res) => {
        addressJson.value = userClassPinyin(res)
    }).catch((err) => {
        console.log(err,'err');
        uni.showToast({
            title: '获取地址级联数据失败',
            icon: 'none'
        })
        
    })
}
const stepsShow = ref(true)
const disabled = ref(true)

const activeId = ref("0")
// 级联地址点击事件
const radioClick = (item) => {  
    activeId.value = item.id
    
    switch (steps.value.length) {
        case 1:
            changeSteps(1, textMap[1], item)
            getAddressData(item.code,item.cateType + 1)
            break;
        case 2:
            changeSteps(2, textMap[2], item)
            getAddressData(item.code,item.cateType + 1)
            break;
        case 3:
            changeSteps(3, textMap[3], item)
            getAddressData(item.code,item.cateType + 1)
            break;
        case 4:
            stepsShow.value = false
            steps.value[3].text = item.name
            steps.value[3].code = item.code
            setTimeout(() => {
                stepsShow.value = true
            }, 2);
            break;
        default:
            break;
    }
    disabled.value = !!!steps.value[2]?.code
}

// 修改步骤条并且获取地址数据
const changeSteps = (num: number, text: string, item) => {
    stepsShow.value = false
    steps.value[num - 1].text = item.name
    steps.value[num - 1].code = item.code
    steps.value.push({
        text,
        code: ''
    })
    setTimeout(() => {
        stepsShow.value = true
    }, 2);
}

// 重置
const resetFn = () => {
    steps.value = [
        {
            text: textMap[0],
            code: ''
        },
    ]
    getAddressData("0",1)
}
// 确定地址
const confirmAddress = () => {
    
    const addressInfo = [{
        text: 'province',
        code: 'provinceId'
    }, {
        text: 'cityName',
        code: 'cityId'
    }, {
        text: 'area',
        code: 'areaId'
    }, {
        text: 'town',
        code: 'townId'
    }]
    // 先清空form中的地址信息
    addressInfo.forEach(element => {
        form[element.text] = ''
        form[element.code] = ''
    });
    // 根据步骤条的值修改form中的地址信息
    for (let i = 0; i < steps.value.length; i++) {
        if (steps.value[i].code == '') continue
        form[addressInfo[i].text] = steps.value[i].text
        form[addressInfo[i].code] = steps.value[i].code
    }
    popupClose()
}

// 根据传入的key值修改对应的form值
const onChange = (key: string, e) => {
    form[key] = e.detail;
}

const defaultSetFn = (e) => {
    form.isDefault = e.detail ? 1 : 0
}

const popupShow = ref(false)
// 显示popup
const showPopupFn = () => {
    popupShow.value = true
}
// 关闭popup
const popupClose = () => {
    popupShow.value = false
}
// 地址步骤条数据
const steps = ref([
    {
        text: textMap[0],
        code: ''
    },
])


// 点击步骤条
const clickStep = (e) => {
    
    // 重新选择当前步骤的地址
    steps.value = steps.value.slice(0, e.detail)
    steps.value.push({
        text: textMap[e.detail] || '选择街道',
        code: ''
    });
    const code = e.detail == "0" ? "0" : steps.value[e.detail - 1].code
    getAddressData(code,e.detail+1)

}

interface Errors {
    rulesName: string,
    rulesCardId: string
}
// 错误信息
const errors = reactive({
    name: '',
    mobile: '',
    address: '',
    province: '',
    city: '',
    area: '',
})
// 取消
const onCancel = () => {
    navigateBack()
}
// 保存
const confirm = () => {
    // 判断必须条件
    if (isEmpty(form.name) || isEmpty(form.mobile) || isEmpty(form.province) || isEmpty(form.address) ) {
        uni.showToast({
            title: '请填写完整信息',
            icon: 'none',
        })
        return
    }
    if (type.value != AddressTypeEnum.edit) {
        const addAddressApi = placeOrder.value == '1' ? addPlaceOrderAddress : addAddress
        const data = Object.assign({customerId:''}, form)
        addAddressApi(data).then((res) => {
            if (type.value == AddressTypeEnum.orderAdd) {
                uni.$emit('getAddress', res)
                navigateBack()
            } else {
                navigateBack()
            }
        }).catch((err) => {
            uni.showToast({
                title: err.data.message,
                icon: 'none',
            })
        })
    } else {
        const editAddressApi = placeOrder.value == '1' ? editPlaceOrderAddress : editAddress
        editAddressApi(form).then((res) => {
            navigateBack()
        }).catch((err) => {
            uni.showToast({
                title: err.data.message,
                icon: 'none',
            })
        })
    }
}
</script>

<style scoped lang="scss" >
.verify {
    padding-left: 20rpx;

    .form {
        .selectAddress {
            width: 100%;
        }
    }

    // popup样式
    .popup-box {
        padding: 20rpx;
        box-sizing: border-box;
        height: 1008rpx;
        position: relative;

        .popup-heard {
            .heard-title {
                height: 60rpx;
                line-height: 60rpx;
                text-align: center;
                font-weight: 600;
                font-size: 32rpx;
                padding-bottom: 20rpx;
            }

            .popup-steps {
                border-bottom: 1px solid #EEEEEE;
                padding-bottom: 20rpx;
                min-height: 100rpx;

                ::v-deep .van-step--vertical:after {
                    border-bottom-width: 0rpx; // 清除vant组件的下边框
                }

                ::v-deep .van-step__title {
                    color: black !important;

                    &::after {
                        content: "";
                        display: block;
                        width: 20rpx;
                        height: 20rpx;
                        background: url("@/static/images/user/rightArrow.png") no-repeat center center;
                        background-size: 150% 150%;
                        position: absolute;
                        right: 20rpx;
                        top: calc(50% - 10rpx);
                    }
                }
            }
        }

        .popup-content {
            padding-bottom: 140rpx;

            .popup-selectAddress {
                .selectAddress-title {
                    padding: 20rpx 0rpx;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #333333;
                    line-height: 33rpx;
                }

                .selectAddress-content {
                    // border: 1px solid black;
                    font-size: 24rpx;

                    .content-flex {
                        display: flex;

                        .address-py {
                            width: 50rpx;
                            text-align: center;
                            line-height: 60rpx;
                            font-size: 24rpx;
                            color: #666666;
                        }

                        .address-radio-group {
                            width: 100%;

                            .address-radio {
                                line-height: 60rpx;
                            }

                            .active {
                                color: var(--primary-color);
                            }
                        }
                    }
                }
            }
        }

        .popup-footer {
            display: flex;
            justify-content: space-between;
            padding-top: 20rpx;
            height: 120rpx;
            background: #ffffff;
            position: fixed;
            bottom: 0rpx;
            width: calc(100% - 40rpx);

            .leftBtn,
            .rightBtn {
                width: 48%;
            }

            .leftBtn {
                ::v-deep button {
                    width: 100%;
                    color: #000 !important;
                }
            }
        }
    }

    .phoneInput {
        display: flex;
        align-items: center;

        view {
            margin-right: 20rpx;
        }

        input {
            line-height: 80rpx !important;
            height: 80rpx;
        }
    }

    .detailAddress {
        ::v-deep .van-field__body--textarea {
            background: #F8F8F8;
            padding: 20rpx;
        }
    }

    .defaultSet {
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
    }
}

.label {
    line-height: 80rpx;

    .requiredIcon {
        color: var(--primary-color);
    }
}

::v-deep .van-field__body--text {
    height: 80rpx;
    background: #F8F8F8;
    padding: 20rpx;
    box-sizing: border-box;
}

::v-deep .van-field__label {
    line-height: 80rpx !important;
}

.footerBtn {
    width: 100%;
    box-sizing: border-box;
    padding: 0rpx 0rpx 20rpx 0rpx;
    position: fixed;
    bottom: 0px;
    left: 0rpx;
}</style>