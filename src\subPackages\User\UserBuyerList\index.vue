<template>
    <view class="customer">
        <scroll-view class="customer-list" scroll-y :refresher-threshold="100">
            <view class="customer-item" v-for="item in customerList" @click="handleSaveDrugUser(item)" :key="item.id">
                <view class="item-con">
                    <text style="font-weight: bold;">{{ item.name }}</text>
                    <view class="item-icon">
                        <view class="icon-edit" @click.stop="handleCustomerDrugUser('edit',item.id)">
                            <van-image width="32rpx" height="32rpx" fit="contain" :src="Edit" style="margin-right: 16rpx;" />
                        </view>
                        <view class="icon-delete" @click.stop="handleCustomerDrugDelete(item.id)">
                            <van-image width="32rpx" height="32rpx" fit="contain" :src="Delete" />
                        </view>
                    </view>
                </view>
                <text style="font-size: 24rpx;">{{ item.gender }} {{ item.age+'岁' }}</text>
            </view>
            <LoadLoading :show='customerLoading'/>
            <viwe class="noData" v-if="!customerLoading && !customerList.length ">
                <viwe class="noData-drug">
                    <van-image width="400rpx" fit="contain" height="400rpx" :src="NotCustomerDrug"/>
                    <text class="noData-tit">暂无用药人信息</text>
                </viwe>
                <van-button v-if="token" class="addBtn" @click.stop="handleCustomerDrugUser('add')" round block color="var(--primary-color-gradient)">新增用药人</van-button>
                <van-button v-else class="addBtn" @click.stop="jumpToUrl('Login')" round block color="var(--primary-color-gradient)">登录查看用药人</van-button>
            </viwe>
        </scroll-view>
        <FooterBtn v-if="customerList.length" :title="'新增用药人'" :handle-submit="() => handleCustomerDrugUser('add')"></FooterBtn>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad,onShow } from "@dcloudio/uni-app";
import FooterBtn from "../UserBuyerEdit/components/footerBtn.vue";
import NotCustomerDrug from '@/static/images/drug/notCustomerDrug.png'
import Delete from '@/static/images/user/delete.png'
import Edit from '@/static/images/user/edit.png'
import LoadLoading from "@/components/LoadLoading/index.vue"
import { navigateTo } from '@/routes/utils/navigateUtils';
import { RouteName } from "@/routes/enums/routeNameEnum";
import { userCustomerListData } from '../../../hooks/userCustomerListData'
import { storeToRefs } from "pinia";
import { userInfoStore } from "@/stores/modules/user";
import { useCommon } from "@/hooks";

const {
    getCustomerDrugList,
    customerDrugUserDelete,
    customerDrugUserSave,
    customerList,
    customerLoading,
    isPageList,
    customerType
} = userCustomerListData()
const { jumpToUrl } = useCommon()
const { token } = storeToRefs(userInfoStore())
const pageType = ref<string>('my') // my = 我的页面
const handleSaveDrugUser = (item)=>{
    customerDrugUserSave(item,pageType.value)
}
function handleCustomerDrugDelete(id){
    customerDrugUserDelete(id)
}
function handleCustomerDrugUser(type,userId?){
    navigateTo({
        url:RouteName.UserBuyerEdit,
        props:{
            id:userId ?? '',
            type,
        }
    })
}
onLoad((e)=>{
    pageType.value = e.type ?? 'my'
    customerType.value = e.customerType ?? 'customer'
    if(!token.value){
        isPageList.value = true
    }else{
        getCustomerDrugList()
    }
})
// 控制登录返回请求
onShow(()=>{
    if(isPageList.value && token.value){
        isPageList.value = false
        getCustomerDrugList()
    }
})
</script>

<style scoped lang="scss" >
.customer{
    height: 100vh;
    background: #F8F8F8;
    .customer-list{
        box-sizing: border-box;
        height: calc(100vh - 180rpx);
        padding: 24rpx;
        .customer-item{
            height: 134rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            padding: 24rpx;
            box-sizing: border-box;
            background: #FFFFFF;
            margin-bottom: 16rpx;
            border-radius: 16rpx;
            .item-con{
                display: flex;
                justify-content: space-between;
                .item-icon{
                    display: flex;
                    .icon-edit,.icon-delete{
                        width: 32rpx;
                        height:32rpx
                    }
                    .icon-delete{
                        margin-left:16rpx;
                    }
                }
            }
        }
        .noData,.noData-drug{
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .noData{
            padding-top: 192rpx;
        }
        .noData-tit{
            font-size: 28rpx;
            color: #666666;
        }
    }
    .addBtn{
        width: 448rpx;
        height: 80rpx;
        margin-top: 400rpx;
    }
}
</style>