<template>
  <view class="chinese-medicine-list">
    <view class="medicine-grid">
      <!-- 单列 -->
      <view class="medicine-column">
        <view
          v-for="(medicine, index) in medicineListWithIndex"
          :key="index"
          class="medicine-item"
          :class="{ 'has-next': index < medicineListWithIndex.length - 1 }"
        >
          <view class="medicine-number">{{ medicine.originalIndex + 1 }}</view>
          <view class="medicine-info">
            <text class="medicine-name">{{ medicine.productName }}</text>
            <!-- <text class="medicine-dosage"
              >{{ medicine.count }}{{ medicine.specName }}</text
            > -->
            <text class="medicine-dosage" >({{decoctionMethod_list[medicine.decoction - 1]}})</text>
          </view>
          <!-- 虚线连接 -->
          <view
            v-if="index < medicineListWithIndex.length - 1"
            class="dashed-line"
          ></view>
        </view>
      </view>
    </view>
    <!-- 中药处方信息 -->
  <view class="chinese-medicine-message" >
    共{{ rpList[0].chineseDosageCount || 0 }}剂  {{ getRouteProperty(rpList[0],'routeOfAdministration',
            'route')?routeOfAdministration_list[getRouteProperty(rpList[0],'routeOfAdministration',
            'route')]:getRouteProperty(rpList[0],'routeOfAdministrationOther',
            'custRoute') }}

            {{ getRouteProperty(rpList[0],'frequencyOfAdministration',
            'freq')?frequencyOfAdministration_list[getRouteProperty(rpList[0],'frequencyOfAdministration',
            'freq')]:getRouteProperty(rpList[0],'frequencyOfAdministrationOther',
            'custFreq') }}

            每次{{rpList[0].dosage}} {{dosageUnits_list[getRouteProperty(rpList[0],'units',
            'dosageUnits')-1]}}

  </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { presRpListDTO } from "@/components/PharmaceuticalInformation/hook/type";
import { PrescriptionDrugType } from "@/enum/prescriptionsEnum"
import pharmaceuticalInformation from "@/components/PharmaceuticalInformation/hook/pharmaceuticalInformation";
const {routeOfAdministration_list, frequencyOfAdministration_list,dosageUnits_list,decoctionMethod_list,getRouteProperty} = pharmaceuticalInformation()


// 定义中药材接口
interface Medicine {
  productName: string;
  dosage: string;
}


// 定义props
const props = defineProps<{
  rpList?: presRpListDTO[];
}>();

// 计算属性：使用传入的数据或默认数据
const medicineList = computed(() => {
  return props.rpList;
});

// 带索引的药材列表
const medicineListWithIndex = computed(() => {
  return medicineList.value.map((medicine, index) => ({
    ...medicine,
    originalIndex: index,
  }));
});


</script>
<style scoped lang="less">
.chinese-medicine-list {
  background: #ffffff;

  .medicine-grid {
      padding: 24rpx 0rpx;
    .medicine-column {
      overflow: hidden;
      .medicine-item {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .medicine-number {
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background: #e8f4ff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20rpx;
          color: #1989fa;
          font-weight: 600;
          margin-right: 16rpx;
          flex-shrink: 0;
          position: relative;
          z-index: 2;
        }

        .medicine-info {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0; // 确保可以收缩

          .medicine-name {
            font-size: 28rpx;
            color: #333333;
            margin-right: 16rpx;
            font-weight: 500;
            flex-shrink: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 30vw; // 限制最大宽度
          }

          .medicine-dosage {
            font-size: 28rpx;
            color: #666666;
            flex-shrink: 0;
          }
        }

        // 虚线连接
        .dashed-line {
          position: absolute;
          left: 15rpx; // 序号中心位置
          top: 32rpx; // 从序号底部开始
          width: 2rpx;
          height: 32rpx; // 到下一个序号顶部
          background: linear-gradient(to bottom, #8b8b8b 50%, transparent 50%);
          background-size: 2rpx 8rpx;
          z-index: 1;
        }
      }
    }
  }
  .chinese-medicine-message{
    font-size: 24rpx;
    color: #999999;
  }
}
</style>
