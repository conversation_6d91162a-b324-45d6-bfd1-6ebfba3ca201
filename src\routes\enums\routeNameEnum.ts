export const enum RouteName{
    /** 商品列表 */
    Cate = 'jw-store-mp-cate',
    /** 疗法列表 */
    Therapy = 'jw-store-mp-therapy',
    /** 商品详情 */
    GoodsDetail = 'jw-store-mp-goods-detail'
}

export const enum RouteName{
    /** 登录 */
    Login = 'jw-store-mp-login',
    /** 登录 */
    jtLogin = 'jw-store-mp-jt-login',
    /** 首页 */
    Home = 'jw-store-mp-home',
    /** 搜索 */
    Search = 'jw-store-mp-search',
    /** webview */
    Webview = 'jw-store-mp-webview',
    /** 问诊 */
    Inquiry = 'jw-store-mp-inquiry',
}

export const enum RouteName{
    /** 订单 */
    Order = 'jw-store-mp-order',
    /** 订单详情 */
    OrderDetail = 'jw-store-mp-order-detail',
    /** 取消订单 */
    CancelDetail = 'jw-store-mp-cancel-detail',
    /** 取消订单 */
    LogisticsDetail = 'jw-store-mp-logistics-detail',
    /** 申请退款 */
    RefundDetails = 'jw-store-mp-refund-detail',
    /** 售后详情 */
    AfterSaleDetail = 'jw-store-mp-after-sale-detail',
    /** 订单确认 */
    OrderConfirm='jw-store-mp-order-confirm',
    /** 支付页面 */
    Pay = 'jw-store-mp-order-pay',
    /** 代下单*/
    OrderAgent = 'jw-store-mp-order-agent',
    /** 代下单补充订单信息 */
    OrderSupplementary = 'jw-store-mp-order-supplementary',
     /** 代下单结果页 */
    OrderAgentResult = 'jw-store-mp-order-result',
}

export const enum RouteName{
    /** 处方 */
    Prescription = 'jw-store-mp-prescription',
    /** 处方下单 */
    PrescriptionPlaceOrder = 'jw-store-mp-prescription-place-order',
    /** 处方表单 */
    PrescriptionForm = 'jw-store-mp-prescription-form',
    /** 处方列表 */
    PrescriptionList='jw-store-mp-prescription-list',
    /** 处方详情 */
    PrescriptionDetail='jw-store-mp-prescription-detail',
    /** 处方单列表 */
    ListOfPrescriptions = 'jw-store-mp-list-of-prescriptions',
    /** 处方单详情 */
    PrescriptionFormDetail = 'jw-store-mp-list-of-prescriptions-form-detail',
}

export const enum RouteName{
    /** 购物车 */
    Cart = 'jw-store-mp-cart',

    /** 商城视频 */
    Video = 'jw-store-mp-video',
    /** 收藏页视频详情 */
    CollectionVideoDetail = 'jw-store-mp-collection-video-detail',

    /** 视频我的主页*/
    VideoMyPage = 'jw-store-mp-video-my-page',
}

export const enum RouteName{
    /** 我的 */
    User = 'jw-store-mp-user',
    /** 用户设置 */
    UserSettings = 'jw-store-mp-user-settings',
    /** 用户认证 */
    UserVerify = 'jw-store-mp-user-verify',
    /** 用户认证结果 */
    UserVerifyResult = 'jw-store-mp-user-verify-result',
    /** 用户地址 */
    UserAddress = 'jw-store-mp-user-address',
    /** 用户地址编辑 */
    UserAddressEdit = 'jw-store-mp-user-address-edit',
    /** 用药人列表 */
    UserBuyerList = 'jw-store-mp-user-buyer-list',
    /** 用药人信息编辑 */
    UserBuyerEdit = 'jw-store-mp-user-buyer-edit',
    /** 问诊列表 */
    ListOfMedicalConsultations = 'jw-store-mp-list-of-medical-consultations',
    /** 问诊详情 */
    MedicalConsultationFormDetail = 'jw-store-mp-list-of-medical-consultations-form-detail',
    /** 消息列表 */
    Message = 'jw-store-mp-message',
    /** 我的医生 */
    MyDoctorList = 'jw-store-mp-my-doctor-list',
    /** 我的预约 */
    MyAppointment = 'jw-store-mp-my-appointment',
    /** 医助后台 */
    AppointmentForMedicalConsultation = 'jw-store-mp-appointment-for-medical-consultation',
    /** 账户余额 */
    Balance = 'jw-store-mp-balance',
}

export const enum RouteName {
    IntegralHome = 'jw-store-mp-integral-home',
    PointsRecord = 'jw-store-mp-points-record',
    /**我能兑 */
    PointsExchange = 'jw-store-mp-points-exchange',
}

export const enum RouteName {
    /** 分销中心 */
    Distribute = 'jw-store-mp-distribute',
    /** 客户列表 */
    DistributeCustomer = 'jw-store-mp-distribute-customer',
    /** 分销订单 */
    DistributeOrder = 'jw-store-mp-distribute-order',
    /** 分销订单详情 */
    DistributeOrderDetail = 'jw-store-mp-distribute-order-detail',
    /** 分销邀请客户 */
    DistributeInvite = 'jw-store-mp-distribute-invite',
}

export const enum RouteName{
    /** 首页 */
    SHome = 'jw-group-mp-home',
    /** Exam */
    Demo = 'jw-group-mp-demo',
    SWebView = 'jw-group-mp-webview',
    Check = 'jw-group-mp-check',
    /** 资讯 */
    News = 'jw-group-mp-news',
}

export const enum RouteName{
    /** 医生列表 */
    InquiryDoctorList = 'jw-group-mp-doctor-list',
    /** 医生搜索 */
    InquiryDoctorSearch = 'jw-group-mp-doctor-search',
    /** 医生首页 */
    InquiryDoctorDetail = 'jw-group-mp-doctor-detail',
    /** 症状描述 */
    InquirySymptomDescription = 'jw-group-mp-symptom-description',
    /** 待接诊 */
    InquiryPending = 'jw-group-mp-pending',
    /** 问诊聊天 */
    InquiryChat = 'jw-group-mp-inquiry-chat',
}

export const enum RouteName{
    /** 直播 */
    Stream = 'jw-store-mp-stream',
}

export const enum RouteName{
    /** 视频问诊页面 */
    Live = 'jw-store-mp-live',
}

