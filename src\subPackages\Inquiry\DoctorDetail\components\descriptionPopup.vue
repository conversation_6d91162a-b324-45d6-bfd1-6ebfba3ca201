<template>
  <van-popup
    v-model:show="showPopup"
    round
    position="bottom"
    custom-style="height: 50%"
    close-on-click-overlay
    @close="onClose"
  >
    <view class="description-popup-content">
       <view class="description-popup-content-item" v-if="speciality">
            <view class="description-popup-content-title">
                <view class="description-popup-content-title-line"></view>
                <view class="description-popup-content-title-text">擅长</view>
            </view>
            <view class="description-popup-content-speciality">
                {{ speciality }}
            </view>
       </view>
       <view class="description-popup-content-item" v-if="description">
            <view class="description-popup-content-title">
                <view class="description-popup-content-title-line"></view>
                <view class="description-popup-content-title-text">简介</view>
            </view>
            <view class="description-popup-content-speciality">
                {{ description }}
            </view>
       </view>
    </view>
  </van-popup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
const props = withDefaults(
  defineProps<{
    show: boolean;
    speciality: string;
    description: string;
  }>(),
  {
    show: false,
    speciality: "",
    description: "",
  }
);

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
}>();

const showPopup = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

const onClose = () => {
  emit("update:show", false);
};
</script>

<style lang="scss" scoped>
    .description-popup-content{
        display: flex;
        flex-direction: column;
        gap: 24rpx;
        padding: 32rpx;
        box-sizing: border-box;
        .description-popup-content-item{
            display:flex;
            flex-direction: column;
            gap: 24rpx;
            margin-bottom: 24rpx;
            .description-popup-content-title{
                display: flex;
                align-items: center;
                gap: 16rpx;
                .description-popup-content-title-line{
                    width: 10rpx;
                    height: 32rpx;
                    background: #1677FF;
                }
                .description-popup-content-title-text{
                    font-size: 40rpx;
                    color: #333333;
                    font-weight: bold;
                }
            }
            .description-popup-content-speciality{
                line-height: 44rpx;
                color: #666666;
                font-size: 28rpx;
            }
                
        }
    }
</style>
