import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import { transformImageUrl } from "./scripts/plugins/transform";
import { delFileByPath } from "./scripts/plugins/delFileByPath";
import { largeImageSrcList } from "./scripts/preBuild/largeImageSrcList";
import { loadEnv } from "vite";

const HTTPURLPREFIX = "https://store.loeow6.cn";


// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  let plugins:Array<any> = [uni()]
  if(process.env.NODE_ENV == 'production'){
    plugins = [
      transformImageUrl(largeImageSrcList, HTTPURLPREFIX),
      uni(),
      // 根据路径删除文件
      delFileByPath(largeImageSrcList),
    ]
  }
  return {
    plugins
  }
})


