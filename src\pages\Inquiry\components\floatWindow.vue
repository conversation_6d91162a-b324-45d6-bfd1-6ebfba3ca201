<template>
  <view class="float-window-container" :style="customStyle" ref="containerRef">
    <image
      class="float-window"
      :src="modalImg"
      mode="scaleToFill"
      :style="floatWindowStyle"
      ref="floatWindowRef"
      @click="navigateTo({url:RouteName.InquiryOfficial})"
    />
    <!-- <van-popup
      :show="isShow"
      @close="onClose"
      root-portal
      round
      custom-style="background-color: transparent;overflow: visible;"
      :close-on-click-overlay="false"
    >
      <view class="modal-popup">
        <image :src="modalImg" mode="scaleToFill" class="modal-img" @click="navigateTo({url:RouteName.InquiryOfficial})" />
        <view class="closeIcon" @click="onClose">
          <image class="close-img" :src="closeBtn" mode="scaleToFill" />
        </view>
      </view>
    </van-popup> -->
  </view>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  StyleValue,
  getCurrentInstance,
} from "vue";
import floatIcon from "@/static/images/inquiry/25126/float.png";
import modalImg from "@/static/images/inquiry/25126/modal.png";
import closeBtn from "@/static/images/popup/closeBtn.png";
import { RouteName } from "@/routes/enums/routeNameEnum";
import { navigateTo } from "@/routes/utils/navigateUtils";
import { onShow } from "@dcloudio/uni-app";
const props = defineProps<{
  customStyle: StyleValue;
}>();

const isShow = ref(false);
const onClose = () => {
    isShow.value = false
};

onShow(()=>{
    isShow.value = false
})

// 容器和漂浮窗的引用
const containerRef = ref<HTMLElement>();
const floatWindowRef = ref<HTMLElement>();

// 漂浮窗的位置和速度
const position = ref({
  x: 10, // 初始x位置
  y: 200, // 初始y位置
});

const velocity = ref({
  x: 1, // x方向速度
  y: 0.75, // y方向速度
});

// 漂浮窗宽度（rpx转px，大概是rpx/2）
const FLOAT_WINDOW_WIDTH_SIZE = 110; 
const FLOAT_WINDOW_HRIGHT_SIZE = 80; 

// 容器尺寸
const containerSize = ref({
  width: 350, // 增加默认宽度
  height: 500, // 增加默认高度
});

// 动画定时器ID
let animationTimerId: number | null = null;

// 计算漂浮窗样式
const floatWindowStyle = computed(() => ({
  left: `${position.value.x}px`,
  top: `${position.value.y}px`,
  position: "absolute" as const,
}));

// 获取当前组件实例
const instance = getCurrentInstance();

// 获取容器尺寸
const getContainerSize = () => {
  return new Promise((resolve) => {
    if (!instance) {
      console.log("无法获取组件实例，使用默认尺寸");
      resolve(null);
      return;
    }

    const _this = instance.proxy;
    const query = uni.createSelectorQuery().in(_this);
    query.select(".float-window-container").boundingClientRect((rect: any) => {
    //   console.log("获取容器尺寸结果:", rect);
      if (rect && rect.width && rect.height) {
        containerSize.value.width = rect.width;
        containerSize.value.height = rect.height;
        // console.log("容器尺寸获取成功:", rect.width, rect.height);
      } else {
        console.log("获取容器尺寸失败，使用默认尺寸");
      }
      resolve(rect);
    });

    query.exec();
  });
};

// 更新漂浮窗位置
const updatePosition = () => {
  // 使用缓存的容器尺寸
  const containerWidth = containerSize.value.width;
  const containerHeight = containerSize.value.height;

  // console.log('更新位置:', position.value.x, position.value.y, '容器尺寸:', containerWidth, containerHeight);

  // 更新位置
  position.value.x += velocity.value.x;
  position.value.y += velocity.value.y;

  // 检查边界碰撞并反弹
  // 左边界
  if (position.value.x <= 0) {
    position.value.x = 0;
    velocity.value.x = Math.abs(velocity.value.x); // 向右反弹
  }

  // 右边界
  if (position.value.x >= containerWidth - FLOAT_WINDOW_WIDTH_SIZE) {
    position.value.x = containerWidth - FLOAT_WINDOW_WIDTH_SIZE;
    velocity.value.x = -Math.abs(velocity.value.x); // 向左反弹
  }

  // 上边界
  if (position.value.y <= 0) {
    position.value.y = 0;
    velocity.value.y = Math.abs(velocity.value.y); // 向下反弹
  }

  // 下边界
  if (position.value.y >= containerHeight - FLOAT_WINDOW_HRIGHT_SIZE) {
    position.value.y = containerHeight - FLOAT_WINDOW_HRIGHT_SIZE;
    velocity.value.y = -Math.abs(velocity.value.y); // 向上反弹
  }
};

// 开始动画
const startAnimation = async () => {
  // 先尝试获取容器尺寸
  try {
    await getContainerSize();
  } catch (error) {
    console.log("获取容器尺寸失败，使用默认尺寸");
  }

//   console.log(
//     "最终使用的容器尺寸:",
//     containerSize.value.width,
//     containerSize.value.height
//   );

  if (animationTimerId) {
    clearInterval(animationTimerId);
  }
  // 使用setInterval替代requestAnimationFrame，大约60fps
  animationTimerId = setInterval(updatePosition, 16);
};

// 停止动画
const stopAnimation = () => {
  if (animationTimerId) {
    clearInterval(animationTimerId);
    animationTimerId = null;
  }
};

// 组件挂载时开始动画
onMounted(() => {
  // 延迟一下确保DOM渲染完成
  setTimeout(async () => {
    // console.log("开始启动动画");
    await startAnimation();
    // console.log("动画启动完成");
  }, 1000);
});

// 组件卸载时停止动画
onUnmounted(() => {
  stopAnimation();
});
</script>
<style scoped lang="scss">
.float-window-container {
  position: relative;
  width: 100%;
  height: 100%;
//   overflow: hidden; // 防止漂浮窗超出容器
  position: fixed;
  top: 0px;
  right: 0px;
  z-index: 99;
  /** 禁用鼠标事件 */
  pointer-events: none;
  .float-window {
    width: 192rpx;
    height: 160rpx;
    // z-index: 999;
    transition: none; // 禁用过渡动画，确保流畅的帧动画
    /** 开启鼠标事件 */
    pointer-events: auto;
    border-radius: 24rpx ;
  }
}
.modal-popup {
  width: 360rpx;
  height: 300rpx;
  .modal-img {
    width: 100%;
    height: 100%;
    border-radius: 32rpx;
  }
  :deep(.closeIcon) {
    width: 68rpx;
    height: 68rpx;
    position: absolute;
    bottom: -100rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    .close-img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
