/*!
 * @byted/miniapp-rtc v3.3.0
 * (c) 2020-2025 The VolcEngineRTC project authors.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).MiniappRtc=t()}(this,(function(){"use strict";var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};function t(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}var n=function(){return(n=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function o(e,t,n,o){var i,s=arguments.length,r=s<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,n,o);else for(var c=e.length-1;c>=0;c--)(i=e[c])&&(r=(s<3?i(r):s>3?i(t,n,r):i(t,n))||r);return s>3&&r&&Object.defineProperty(t,n,r),r}function i(e,t,n,o){return new(n||(n=Promise))((function(i,s){function r(e){try{a(o.next(e))}catch(e){s(e)}}function c(e){try{a(o.throw(e))}catch(e){s(e)}}function a(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(r,c)}a((o=o.apply(e,t||[])).next())}))}function s(e,t){var n,o,i,s,r={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function c(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,o&&(i=2&s[0]?o.return:s[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,s[1])).done)return i;switch(o=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,o=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!(i=r.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(6===s[0]&&r.label<i[1]){r.label=i[1],i=s;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(s);break}i[2]&&r.ops.pop(),r.trys.pop();continue}s=t.call(e,r)}catch(e){s=[6,e],o=0}finally{n=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function r(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,i,s=n.call(e),r=[];try{for(;(void 0===t||t-- >0)&&!(o=s.next()).done;)r.push(o.value)}catch(e){i={error:e}}finally{try{o&&!o.done&&(n=s.return)&&n.call(s)}finally{if(i)throw i.error}}return r}var c,a,u=function(e){var t={exports:{}};return e(t,t.exports),t.exports}((function(e){var t=Object.prototype.hasOwnProperty,n="~";function o(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function s(e,t,o,s,r){if("function"!=typeof o)throw new TypeError("The listener must be a function");var c=new i(o,s||e,r),a=n?n+t:t;return e._events[a]?e._events[a].fn?e._events[a]=[e._events[a],c]:e._events[a].push(c):(e._events[a]=c,e._eventsCount++),e}function r(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function c(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),c.prototype.eventNames=function(){var e,o,i=[];if(0===this._eventsCount)return i;for(o in e=this._events)t.call(e,o)&&i.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},c.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var i=0,s=o.length,r=new Array(s);i<s;i++)r[i]=o[i].fn;return r},c.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},c.prototype.emit=function(e,t,o,i,s,r){var c=n?n+e:e;if(!this._events[c])return!1;var a,u,d=this._events[c],_=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),_){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,o),!0;case 4:return d.fn.call(d.context,t,o,i),!0;case 5:return d.fn.call(d.context,t,o,i,s),!0;case 6:return d.fn.call(d.context,t,o,i,s,r),!0}for(u=1,a=new Array(_-1);u<_;u++)a[u-1]=arguments[u];d.fn.apply(d.context,a)}else{var l,h=d.length;for(u=0;u<h;u++)switch(d[u].once&&this.removeListener(e,d[u].fn,void 0,!0),_){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,t);break;case 3:d[u].fn.call(d[u].context,t,o);break;case 4:d[u].fn.call(d[u].context,t,o,i);break;default:if(!a)for(l=1,a=new Array(_-1);l<_;l++)a[l-1]=arguments[l];d[u].fn.apply(d[u].context,a)}}return!0},c.prototype.on=function(e,t,n){return s(this,e,t,n,!1)},c.prototype.once=function(e,t,n){return s(this,e,t,n,!0)},c.prototype.removeListener=function(e,t,o,i){var s=n?n+e:e;if(!this._events[s])return this;if(!t)return r(this,s),this;var c=this._events[s];if(c.fn)c.fn!==t||i&&!c.once||o&&c.context!==o||r(this,s);else{for(var a=0,u=[],d=c.length;a<d;a++)(c[a].fn!==t||i&&!c[a].once||o&&c[a].context!==o)&&u.push(c[a]);u.length?this._events[s]=1===u.length?u[0]:u:r(this,s)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&r(this,t)):(this._events=new o,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=n,c.EventEmitter=c,e.exports=c}));!function(e){e.userLeave="userLeave",e.connectionLost="connectionLost",e.roleChanged="roleChanged",e.userDuplicateLogin="userDuplicateLogin",e.kickedByAdmin="kickedByAdmin",e.roomDismissed="roomDismissed",e.roomForbidden="roomForbidden",e.userForbidden="userForbidden",e.serverError="serverError"}(c||(c={})),function(e){e.PEER_ONLINE="peer-online",e.PEER_LEAVE="peer-leave",e.CLIENT_BANNED="client-banned",e.STREAM_ADDED="stream-added",e.STREAM_REMOVED="stream-removed",e.UNMUTE_AUDIO="unmute-audio",e.MUTE_AUDIO="mute-audio",e.UNMUTE_VIDEO="unmute-video",e.MUTE_VIDEO="mute-video",e.DISCONNECT="disconnect",e.ERROR="error",e.CLOSE="close",e.UPDATE_URL="update-url",e.LOGIN_BANNED="login-banned",e.STREAM_FAILED="stream-failed",e.USER_MESSAGE_RECEIVED="user-message-received",e.USER_BINARY_MESSAGE_RECEIVED="user-binary-message-received",e.ROOM_MESSAGE_RECEIVED="room-message-received",e.ROOM_BINARY_MESSAGE_RECEIVED="room-binary-message-received",e.USER_MESSAGE_RECEIVED_OUTSIDE_ROOM="user-message-received-outside-room",e.USER_BINARY_MESSAGE_RECEIVED_OUTSIDE_ROOM="user-binary-message-received-outside-room",e.SERVER_PARAMS_SET_RESULT="server-params-set-result"}(a||(a={}));for(var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_="undefined"==typeof Uint8Array?[]:new Uint8Array(256),l=0;l<d.length;l++)_[d.charCodeAt(l)]=l;var h,p,m,f,v,g,I,E=function(e){var t,n=new Uint8Array(e),o=n.length,i="";for(t=0;t<o;t+=3)i+=d[n[t]>>2],i+=d[(3&n[t])<<4|n[t+1]>>4],i+=d[(15&n[t+1])<<2|n[t+2]>>6],i+=d[63&n[t+2]];return o%3==2?i=i.substring(0,i.length-1)+"=":o%3==1&&(i=i.substring(0,i.length-2)+"=="),i},S=function(e){var t,n,o,i,s,r=.75*e.length,c=e.length,a=0;"="===e[e.length-1]&&(r--,"="===e[e.length-2]&&r--);var u=new ArrayBuffer(r),d=new Uint8Array(u);for(t=0;t<c;t+=4)n=_[e.charCodeAt(t)],o=_[e.charCodeAt(t+1)],i=_[e.charCodeAt(t+2)],s=_[e.charCodeAt(t+3)],d[a++]=n<<2|o>>4,d[a++]=(15&o)<<4|i>>2,d[a++]=(3&i)<<6|63&s;return u},y=function(){for(var e=[],t="0123456789abcdef",n=0;n<36;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")};!function(e){e[e.SUCCESS=0]="SUCCESS",e[e.INVALID_TOKEN=-1e3]="INVALID_TOKEN",e[e.LOGIN_FAILED=-1001]="LOGIN_FAILED",e[e.INVALID_USERID=-1002]="INVALID_USERID",e[e.SERVER_ERROR=-1003]="SERVER_ERROR"}(p||(p={})),function(e){e[e.NORMAL_ORDERED=0]="NORMAL_ORDERED",e[e.FAST_ORDERED=1]="FAST_ORDERED",e[e.FAST_UNORDERED=2]="FAST_UNORDERED"}(m||(m={})),function(e){e[e.LOGOUT=0]="LOGOUT",e[e.DUPLICATE_LOGIN=1]="DUPLICATE_LOGIN"}(f||(f={})),function(e){e[e.OFFLINE=0]="OFFLINE",e[e.ONLINE=1]="ONLINE",e[e.UNREACHABLE=2]="UNREACHABLE"}(v||(v={})),function(e){e[e.SUCCESS=0]="SUCCESS",e[e.TIMEOUT=1]="TIMEOUT",e[e.NETWORK_DISCONNECTED=2]="NETWORK_DISCONNECTED",e[e.NO_RECEIVER=3]="NO_RECEIVER",e[e.NO_RELAY_PATH=4]="NO_RELAY_PATH",e[e.EXCEED_QPS=5]="EXCEED_QPS",e[e.E2BS_SEND_FAILED=17]="E2BS_SEND_FAILED",e[e.E2BS_RETURN_FAILED=18]="E2BS_RETURN_FAILED",e[e.NOT_JOIN=100]="NOT_JOIN",e[e.INIT=101]="INIT",e[e.NO_CONNECTION=102]="NO_CONNECTION",e[e.EXCEED_MAX_LENGTH=103]="EXCEED_MAX_LENGTH",e[e.EMPTY_USER=104]="EMPTY_USER",e[e.NOT_LOGIN=105]="NOT_LOGIN",e[e.SERVER_PARAMS_NOT_SET=106]="SERVER_PARAMS_NOT_SET",e[e.UNKNOWN=1e3]="UNKNOWN"}(g||(g={})),function(e){e[e.SUCCESS=200]="SUCCESS",e[e.TIMEOUT=1]="TIMEOUT",e[e.NETWORK_DISCONNECTED=2]="NETWORK_DISCONNECTED",e[e.EXCEED_QPS=5]="EXCEED_QPS",e[e.NOT_JOIN=100]="NOT_JOIN",e[e.NO_CONNECTION=102]="NO_CONNECTION",e[e.EXCEED_MAX_LENGTH=103]="EXCEED_MAX_LENGTH",e[e.UNKNOWN=1e3]="UNKNOWN"}(I||(I={}));var R,O=((h={})[g.SUCCESS]="success",h[g.TIMEOUT]="timeout, failed to send.",h[g.NETWORK_DISCONNECTED]="network disconnected, failed to send.",h[g.NO_RECEIVER]="cannot find the receiver.",h[g.NO_RELAY_PATH]="cannot find relay path.",h[g.EXCEED_QPS]="cannot find relay path.",h[g.E2BS_SEND_FAILED]="failed to send to business server.",h[g.E2BS_RETURN_FAILED]="business server response error.",h[g.NOT_JOIN]="not join room.",h[g.INIT]="not init.",h[g.NO_CONNECTION]="no connection.",h[g.EXCEED_MAX_LENGTH]="message exceeds max length.",h[g.EMPTY_USER]="user id is empty.",h[g.NOT_LOGIN]="not login.",h[g.SERVER_PARAMS_NOT_SET]="server param is not set.",h[g.UNKNOWN]="unknown",h);function b(){var e=void 0,t=void 0,n=new Promise((function(n,o){e=n,t=o}));if(!e||!t)throw Error("Broken Implement of Promise");return{promise:n,resolve:e,reject:t}}!function(e){e[e.INVALID_PARAMS=100001]="INVALID_PARAMS",e[e.NOT_IN_ROOM=100002]="NOT_IN_ROOM",e[e.ALREADY_IN_ROOM=100003]="ALREADY_IN_ROOM",e[e.CONNECTING=100004]="CONNECTING",e[e.ALREADY_PUBLISHED=100005]="ALREADY_PUBLISHED",e[e.NOT_PUBLISHED=100006]="NOT_PUBLISHED",e[e.UID_NOT_FOUND=100007]="UID_NOT_FOUND",e[e.HAS_INITED=100008]="HAS_INITED",e[e.NOT_LOGIN=100009]="NOT_LOGIN",e[e.NO_SERVER_PARAM=100010]="NO_SERVER_PARAM",e[e.STREAM_ID_NOT_FOUND=200001]="STREAM_ID_NOT_FOUND",e[e.GET_CONFIG_ERROR=200002]="GET_CONFIG_ERROR",e[e.NO_WEBSOCLET_URL=200003]="NO_WEBSOCLET_URL",e[e.LOGIN_FAILED=200004]="LOGIN_FAILED",e[e.SUBSCRIBE_FAILED=300001]="SUBSCRIBE_FAILED",e[e.UNSUBSCRIBE_FAILED=300002]="UNSUBSCRIBE_FAILED",e[e.MUTE_LOCAL_ERROR=300003]="MUTE_LOCAL_ERROR",e[e.MUTE_REMOTE_ERROR=300004]="MUTE_REMOTE_ERROR",e[e.LEAVE_ERROR=300005]="LEAVE_ERROR",e[e.PUBLISH_ERROR=300006]="PUBLISH_ERROR",e[e.UNPUBLISH_ERROR=300007]="UNPUBLISH_ERROR",e[e.JOIN_ROOM_ERROR=300008]="JOIN_ROOM_ERROR",e[e.SIGNALING_TIMEOUT=300009]="SIGNALING_TIMEOUT",e[e.WEBSOCKET_NOT_CONNECTED=300010]="WEBSOCKET_NOT_CONNECTED",e[e.KICKED_OUT=300011]="KICKED_OUT",e[e.DUPLICATE_LOGIN=300012]="DUPLICATE_LOGIN",e[e.ROOM_DISMISS=300013]="ROOM_DISMISS",e[e.SERVER_ERROR=300014]="SERVER_ERROR",e[e.LOGIN_ERROR=300015]="LOGIN_ERROR",e[e.SEND_MESSAGE_FAILED=300016]="SEND_MESSAGE_FAILED",e[e.WEBSOCKET_FAILED=500001]="WEBSOCKET_FAILED",e[e.DOMAIN_IN_BLACKLIST=500002]="DOMAIN_IN_BLACKLIST"}(R||(R={}));var N,D=function(){function e(){}return e.setCache=function(e,t){return new Promise((function(n,o){wx.setStorage({key:e,data:t,success:n,fail:o})}))},e.setCacheSync=function(e,t){try{return wx.setStorageSync(e,t),!0}catch(e){return!1}},e.getCache=function(e){return new Promise((function(t,n){wx.getStorage({key:e,success:function(e){t(e)},fail:n})}))},e.getCacheSync=function(e,t){void 0===t&&(t=null);try{var n=wx.getStorageSync(e);return n||t}catch(e){return t}},e.removeStorageSync=function(e){return wx.removeStorageSync(e)},e.removeStorage=function(e){return wx.removeStorage({key:e})},e.getDeviceId=function(){var t=e.getCacheSync(T);return t||(t=y(),e.setCache(T,t),t)},e}(),C="WS_DOMAIN_CACHE",T="device_id";function A(e){return"boolean"==typeof e}!function(e){e.Init="init",e.Connecting="connecting",e.Connected="connected",e.Disconnected="disconnected",e.Reconnecting="reconnecting"}(N||(N={}));var x=function(e){function o(t){var n=e.call(this)||this;return n._state=N.Init,n._enableCamera=!0,n._enableMic=!0,n._publishing=!1,n._isLeaving=!1,n.subscribeSnapshotInfo=[],n._eventCallbackMap=new Map,n._ctx=t.ctx,n._monitor=t.ctx.monitor,n}return t(o,e),o.prototype.connect=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t;return s(this,(function(n){switch(n.label){case 0:if(this._monitor.report("rtc_invoke_status",{sdk_api_name:"room.connect",message:JSON.stringify({roomState:this._state})}),this._state===N.Connected)return[2,0];this._state=N.Connecting,n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this._ctx.connection.connect(e)];case 2:return n.sent(),[3,4];case 3:throw t=n.sent(),this._state=N.Disconnected,t;case 4:return this._handleSignaling(),this._ctx.connection.addCustomer("room"),[2]}}))}))},o.prototype.subscribeSnapshot=function(){var e,t,n=[],o=this._ctx.getRomoteUserMap();try{for(var i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(o.values()),s=i.next();!s.done;s=i.next()){var r=s.value;r.stream&&n.push(r.stream),r.screenStream&&n.push(r.screenStream)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}this.subscribeSnapshotInfo=n},o.prototype.join=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t=this;return s(this,(function(o){return this._checkConnect(),e&&this.subscribeSnapshot(),[2,new Promise((function(o,i){t._joinRoomResolve=o,t._joinRoomReject=i;var s={roomId:t._ctx.roomId,userAttributes:{role:t._ctx.getRole()},params:{deviceType:"miniapp",userAgent:t._ctx.getUA(),sdkVersion:"3.3.0",deviceId:D.getDeviceId(),appId:t._ctx.appId,roomId:t._ctx.roomId,userId:t._ctx.userId,businessId:t._ctx.businessId,rtcSid:""},Authorization:t._ctx.token.startsWith("Basic")?t._ctx.token:"Bearer "+t._ctx.token,_userAgentIP:t._ctx.getUserAgentIP()||"",_lambdaAddr:t._ctx.getLambdaAddr()||""};t._ctx.connection.sendSignaling(e?"reconnected":"joinRoom",s).then((function(e){var n;t._state=N.Connected,t._handleJoinRoomACK(e),null===(n=t._joinRoomResolve)||void 0===n||n.call(t,e)})).catch((function(e){var o;t._state=N.Disconnected,e.code>=700&&e.code<800&&(e.reason="token_error"),null===(o=t._joinRoomReject)||void 0===o||o.call(t,n(n({},e),{code:R.JOIN_ROOM_ERROR}))})).finally((function(){t._ctx.connection.resetRetryCount(),delete t._joinRoomResolve,delete t._joinRoomReject})),t._ctx.utEmitter.emit("afterSendJoinSignaling",s)}))]}))}))},o.prototype.leave=function(){return i(this,void 0,void 0,(function(){var e=this;return s(this,(function(t){return this._isLeaving||this._state===N.Disconnected?[2]:(this._isLeaving=!0,this._checkConnect(),this._joinRoomReject&&this._joinRoomResolve&&(this._joinRoomReject({code:R.JOIN_ROOM_ERROR,reason:"leave_room",message:"call leaveRoom"}),delete this._joinRoomResolve,delete this._joinRoomReject),[2,this._ctx.connection.sendSignaling("leaveRoom",{roomId:this._ctx.roomId,Authorization:this._ctx.token.startsWith("Basic")?this._ctx.token:"Bearer "+this._ctx.token}).finally((function(){e._state=N.Disconnected,e._ctx.connection.deleteCustomer("room"),e._isLeaving=!1}))])}))}))},o.prototype.getLocalStreamId=function(){return this._publishStreamId},o.prototype.destroy=function(){var e;this.removeAllListeners(),this.leave().catch((function(){})),this._ctx.clearRoomInfo(),this._state=N.Init,this._publishRtmp="",this._publishStreamId="",this._enableCamera=!0,this._enableMic=!0,this.subscribeSnapshotInfo=[],this._eventCallbackMap.forEach((function(e){e.reject({code:R.NOT_IN_ROOM})})),this._eventCallbackMap.clear(),null===(e=this._clearListeners)||void 0===e||e.call(this),delete this._clearListeners},o.prototype.publish=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t,n;return s(this,(function(o){switch(o.label){case 0:this._publishing=!0,o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this._publish(e)];case 2:return t=o.sent(),this._publishing=!1,[2,t];case 3:throw n=o.sent(),this._publishing=!1,n;case 4:return[2]}}))}))},o.prototype._publish=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t,o;return s(this,(function(i){switch(i.label){case 0:if(this._checkConnect(),!e&&this._publishRtmp)throw{code:R.ALREADY_PUBLISHED,reason:"published"};i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this._ctx.connection.sendSignaling("publish",{roomId:this._ctx.roomId,screen:!1,streamId:this._publishStreamId||"",eventSessionId:this._ctx.publishSessionId,attributes:{audiostream:this._enableMic,videostream:this._enableCamera},_expectedMSAddr:this._ctx.getMsAddr()})];case 2:return t=i.sent(),this._publishRtmp=t.publishRtmpAddr,this._publishStreamId=t.streamId,[2,{rtmp:this._publishRtmp,streamId:this._publishStreamId}];case 3:throw o=i.sent(),n(n({},o),{code:R.PUBLISH_ERROR});case 4:return[2]}}))}))},o.prototype.unpublish=function(){return i(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:if(!this._publishRtmp)return[2];this._checkConnect(),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this._ctx.connection.sendSignaling("unpublish",{roomId:this._ctx.roomId,streamId:this._publishStreamId,eventSessionId:this._ctx.publishSessionId})];case 2:return t.sent(),this._publishRtmp="",this._publishStreamId="",[3,4];case 3:throw e=t.sent(),n(n({},e),{code:R.UNPUBLISH_ERROR});case 4:return[2]}}))}))},o.prototype.subscribe=function(e,t){return i(this,void 0,void 0,(function(){var o=this;return s(this,(function(i){return this._checkConnect(),[2,new Promise((function(i,s){var r=o._ctx.getUserInfo(e);if(!r)return s({code:R.UID_NOT_FOUND,reason:"uid not found"});var c=t.audio,a=t.video,u=t.screen,d=o._ctx.getRemoteStreamInfo(e,u);if(!d)return s({code:R.STREAM_ID_NOT_FOUND,reason:"stream not found"});d.subOption=t;var _=u?"recvScreenAudio":"recvAudio";"boolean"==typeof r[_]?c=r[_]:"boolean"!=typeof c&&(c=!0);var l=u?"recvScreenVideo":"recvVideo";"boolean"==typeof r[l]?a=r[l]:"boolean"!=typeof a&&(a=!0),o._ctx.connection.sendSignaling("subscribe",{roomId:o._ctx.roomId,streamUserId:e,screen:!!u,uniqueKey:d.uniqueKey,eventSessionId:o._ctx.getSubscribeId(e,u),config:{enableMediaType:{audio:c,video:a}},_expectedMSAddr:o._ctx.getMsAddr()}).then((function(e){t.screen?r.screenStream&&(r.screenStream.rtmp=e.subscribeRtmpAddr,r.screenStream.streamId=e.streamId):r.stream&&(r.stream.rtmp=e.subscribeRtmpAddr,r.stream.streamId=e.streamId),i({rtmp:e.subscribeRtmpAddr,streamId:e.streamId})})).catch((function(e){return s(n(n({},e),{code:R.SUBSCRIBE_FAILED}))}))}))]}))}))},o.prototype.unsubscribe=function(e,t){return i(this,void 0,void 0,(function(){var o=this;return s(this,(function(i){return this._checkConnect(),[2,new Promise((function(i,s){var r,c,a=o._ctx.getUserInfo(e),u="";return a?(u=(null==t?void 0:t.screen)?null===(r=null==a?void 0:a.screenStream)||void 0===r?void 0:r.streamId:null===(c=null==a?void 0:a.stream)||void 0===c?void 0:c.streamId)?void o._ctx.connection.sendSignaling("unsubscribe",{roomId:o._ctx.roomId,streamId:u,eventSessionId:o._ctx.getSubscribeId(e,null==t?void 0:t.screen)}).then((function(){i()})).catch((function(e){return s(n(n({},e),{code:R.UNSUBSCRIBE_FAILED}))})):s({code:R.STREAM_ID_NOT_FOUND,reason:"stream not found"}):s({code:R.UID_NOT_FOUND,reason:"uid not found"})}))]}))}))},o.prototype.muteLocal=function(e){return i(this,void 0,void 0,(function(){var t=this;return s(this,(function(o){return this._checkConnect(),[2,new Promise((function(o,i){if("boolean"==typeof e.audio&&(t._enableMic=!e.audio),"boolean"==typeof e.video&&(t._enableCamera=!e.video),!t._publishRtmp&&!t._publishing)return o();var s={};"boolean"==typeof e.audio&&(s.audiostream=!e.audio),"boolean"==typeof e.video&&(s.videostream=!e.video),t._ctx.connection.sendSignaling("updateStreamAttributes",{roomId:t._ctx.roomId,streamId:t._publishStreamId,attributes:s,eventSessionId:t._ctx.publishSessionId}).then((function(){return o()})).catch((function(e){return i(n(n({},e),{code:R.MUTE_LOCAL_ERROR}))}))}))]}))}))},o.prototype.muteRemote=function(e,t){return i(this,void 0,void 0,(function(){var o=this;return s(this,(function(i){return this._checkConnect(),[2,new Promise((function(i,s){var r,c,a=o._ctx.getUserInfo(e);if(!a)return s({code:R.UID_NOT_FOUND,reason:"uid not found"});var u=void 0;u=t.screen?null===(r=null==a?void 0:a.screenStream)||void 0===r?void 0:r.streamId:null===(c=null==a?void 0:a.stream)||void 0===c?void 0:c.streamId;var d=screen?"recvScreenAudio":"recvAudio";"boolean"==typeof t.audio?a[d]=!t.audio:t.audio="boolean"==typeof a[d]&&!a[d];var _=screen?"recvScreenVideo":"recvVideo";if("boolean"==typeof t.video?a[_]=!t.video:t.video="boolean"==typeof a[_]&&!a[_],!u)return i();o._ctx.connection.sendSignaling("updateSubscribe",{roomId:o._ctx.roomId,streamId:u,config:{enableMediaType:{audio:!t.audio,video:!t.video}},eventSessionId:o._ctx.getSubscribeId(e,t.screen)}).then((function(){return i()})).catch((function(e){return s(n(n({},e),{code:R.MUTE_REMOTE_ERROR}))}))}))]}))}))},o.prototype.updateUserAttributes=function(e){return i(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return this._checkConnect(),this._ctx.setRole(e.role),[4,this._ctx.connection.sendSignaling("updateUserAttributes",{roomId:this._ctx.roomId,attributes:e,eventSessionId:this._ctx.publishSessionId})];case 1:return t.sent(),[2]}}))}))},o.prototype.getStreamInfo=function(e){var t,n,o={streamId:"",rtmp:""},i=this._ctx.getUserInfo(e);return(null==i?void 0:i.stream)&&(o.streamId=null===(t=i.stream)||void 0===t?void 0:t.streamId,o.rtmp=null===(n=i.stream)||void 0===n?void 0:n.rtmp),o},o.prototype.sendUserMessage=function(e){return i(this,void 0,void 0,(function(){var t,n,o;return s(this,(function(i){switch(i.label){case 0:return this._checkConnect(),t=e.message instanceof ArrayBuffer,e.message instanceof ArrayBuffer&&(e.message=E(e.message)),n={userId:e.peerUserId,sessionId:this._ctx.rtcSessionId,eventSessionId:y(),msgId:y(),isBinary:t,msg:e.message},[4,this._sendSignalingWaitEvent("sendUserMessage",n)];case 1:if((o=i.sent()).code!==g.SUCCESS)throw{code:R.SEND_MESSAGE_FAILED,reason:(O[o.code]||"unknown")+"(code="+o.code+")"};return[2]}}))}))},o.prototype.sendRoomMessage=function(e){return i(this,void 0,void 0,(function(){var t,n,o;return s(this,(function(i){switch(i.label){case 0:return this._checkConnect(),t=e.message instanceof ArrayBuffer,e.message instanceof ArrayBuffer&&(e.message=E(e.message)),n={sessionId:this._ctx.rtcSessionId,eventSessionId:y(),msgId:y(),isBinary:t,msg:e.message},[4,this._sendSignalingWaitEvent("sendRoomMessage",n)];case 1:if((o=i.sent()).code!==I.SUCCESS)throw{code:R.SEND_MESSAGE_FAILED,reason:(O[o.code]||"unknown")+"(code="+o.code+")"};return[2]}}))}))},o.prototype._handleSignaling=function(){var e,t=this;null===(e=this._clearListeners)||void 0===e||e.call(this);var n=function(e){t._state=N.Disconnected,t._ctx.connection.deleteCustomer("room")},o=function(){t._state=N.Disconnected,t._ctx.connection.deleteCustomer("room")},i=function(){t.join(!0).then(t._reconnectSuccess.bind(t)).catch((function(e){t._ctx.monitor.report("rtc_error",{error_code:4002,message:"idc 2"+e.reason})}))},s=function(e){t._state===N.Connected&&t._ctx.clientEmit(a.ERROR,e),t._state=N.Disconnected,t._ctx.connection.deleteCustomer("room")},r=this._onUserConnect.bind(this),c=this._onUserDisconnect.bind(this),u=this._onAddStream.bind(this),d=this._onRemoveStream.bind(this),_=this._onStreamFailed.bind(this),l=this._onUpdateStreamAttributes.bind(this),h=function(e){t._onMessageReceived(e,"user")},p=function(e){t._onMessageReceived(e,"room")},m=function(e){var n=e.msgId,o=t._eventCallbackMap.get(n);o&&o.resolve(e)};this._ctx.connection.on("@connection/close",n),this._ctx.connection.on("@connection/disconnected",o),this._ctx.connection.on("@connection/reconnected",i),this._ctx.connection.on("@connection/error",s),this._ctx.connection.on("@signaling/userConnection",r),this._ctx.connection.on("@signaling/userDisconnection",c),this._ctx.connection.on("@signaling/onAddStream",u),this._ctx.connection.on("@signaling/onRemoveStream",d),this._ctx.connection.on("@signaling/streamFailed",_),this._ctx.connection.on("@signaling/onUpdateStreamAttributes",l),this._ctx.connection.on("@signaling/onUserMessageReceived",h),this._ctx.connection.on("@signaling/onRoomMessageReceived",p);var f=Object.values(M);f.forEach((function(e){t._ctx.connection.on(e,m)})),this._clearListeners=function(){t._ctx.connection.off("@connection/close",n),t._ctx.connection.off("@connection/disconnected",o),t._ctx.connection.off("@connection/reconnected",i),t._ctx.connection.off("@connection/error",s),t._ctx.connection.off("@signaling/userConnection",r),t._ctx.connection.off("@signaling/userDisconnection",c),t._ctx.connection.off("@signaling/onAddStream",u),t._ctx.connection.off("@signaling/onRemoveStream",d),t._ctx.connection.off("@signaling/streamFailed",_),t._ctx.connection.off("@signaling/onUpdateStreamAttributes",l),t._ctx.connection.off("@signaling/onUserMessageReceived",h),t._ctx.connection.off("@signaling/onRoomMessageReceived",p),f.forEach((function(e){t._ctx.connection.off(e,m)}))}},o.prototype._handleJoinRoomACK=function(e){var t=this,n=e.clientDetail,o=e.streams,i=new Map(this._ctx.getRomoteUserMap());Array.isArray(n)&&n.forEach((function(e){i.has(e.clientId)?i.delete(e.clientId):t._onUserConnect(e)})),i.forEach((function(e){t._onUserDisconnect({clientId:e.clientId})})),Array.isArray(o)&&o.forEach(this._onAddStream.bind(this))},o.prototype._reconnectSuccess=function(){return i(this,void 0,void 0,(function(){var e,t,n=this;return s(this,(function(o){switch(o.label){case 0:if(!this._publishRtmp)return[3,4];e=this._publishRtmp,o.label=1;case 1:return o.trys.push([1,3,,4]),this._ctx.publishSessionId=y(),[4,this.publish(!0)];case 2:return(t=o.sent().rtmp)!==e&&this._ctx.clientEmit(a.UPDATE_URL,{uid:this._ctx.userId,screen:!1,url:t}),[3,4];case 3:return o.sent(),this._ctx.clientEmit(a.STREAM_FAILED,{uid:this._ctx.userId,screen:!1,reason:"publish failed after reconnection"}),[3,4];case 4:return this.subscribeSnapshotInfo.forEach((function(e){var t;if(null==e?void 0:e.subOption){var o=e.clientId,i=!!(null===(t=e.subOption)||void 0===t?void 0:t.screen);n._ctx.updateSubscribeId(o,i,y()),n.subscribe(o,e.subOption).then((function(e){var t=e.rtmp;n._ctx.clientEmit(a.UPDATE_URL,{uid:o,screen:i,url:t})})).catch((function(e){n._monitor.report("rtc_error",{error_code:4101,message:"resub error: "+o+e.message||e.reason})}))}})),this.subscribeSnapshotInfo=[],[2]}}))}))},o.prototype._onUserConnect=function(e){e.clientId!==this._ctx.userId&&(this._ctx.addRemoteUser(e),this._ctx.clientEmit(a.PEER_ONLINE,{uid:e.clientId}))},o.prototype._onUserDisconnect=function(e){var t=e.clientId,n=e.tag;if(t===this._ctx.userId){var o=null;n===c.kickedByAdmin?o=R.KICKED_OUT:n===c.roomDismissed?o=R.ROOM_DISMISS:n===c.userDuplicateLogin?o=R.DUPLICATE_LOGIN:n===c.serverError&&(o=R.SERVER_ERROR),o&&(this._ctx.clientEmit(a.CLIENT_BANNED,{errorCode:o}),this.emit("@room/"+a.CLIENT_BANNED,{errorCode:o}))}else this._onRemoveStream({clientId:t,screen:!0,roomId:this._ctx.roomId}),this._onRemoveStream({clientId:t,screen:!1,roomId:this._ctx.roomId}),this._ctx.clientEmit(a.PEER_LEAVE,{uid:t}),this._ctx.removeRemoteUser(t)},o.prototype._onAddStream=function(e){var t,n;return i(this,void 0,void 0,(function(){var o,i,r,c,u,d,_;return s(this,(function(s){switch(s.label){case 0:return o=e.streamUserId,i=e.screen,r=this._ctx.getUserInfo(e.streamUserId),c=i?"screenStream":"stream",r?(u=r[c])?[3,1]:(r[c]={clientId:e.streamUserId,uniqueKey:e.uniqueKey,attributes:e.attributes},this._ctx.clientEmit(a.STREAM_ADDED,{uid:o,screen:i,streamPublishState:{audio:e.attributes.audiostream,video:e.attributes.videostream}}),[3,6]):[3,6];case 1:if(u.uniqueKey===e.uniqueKey)return[3,6];this._monitor.report("rtc_invoke_status",{sdk_api_name:"uniqueKey.change",message:JSON.stringify({stream:u,uniqueKey:e.uniqueKey})}),u.uniqueKey=e.uniqueKey,d="",s.label=2;case 2:return s.trys.push([2,4,,5]),this._ctx.updateSubscribeId(o,i,y()),[4,this.subscribe(o,{screen:i,audio:null===(t=u.subOption)||void 0===t?void 0:t.audio,video:null===(n=u.subOption)||void 0===n?void 0:n.video})];case 3:return _=s.sent(),d=_.rtmp,[3,5];case 4:return s.sent(),[3,5];case 5:d?this._ctx.clientEmit(a.UPDATE_URL,{uid:o,screen:i,url:d}):this._ctx.clientEmit(a.STREAM_FAILED,{uid:o,screen:i,reason:"re-subscribe failed."}),s.label=6;case 6:return[2]}}))}))},o.prototype._onRemoveStream=function(e){var t=e.clientId,n=e.screen;if(t!==this._ctx.userId){var o=this._ctx.getUserInfo(t);if(o){if(n){if(!o.screenStream)return;delete o.screenStream}else{if(!o.stream)return;delete o.stream}this._ctx.clientEmit(a.STREAM_REMOVED,{uid:t,screen:n})}}},o.prototype._onStreamFailed=function(e){var t,n;return i(this,void 0,void 0,(function(){var o,i,c,u,d,_,l;return s(this,(function(s){switch(s.label){case 0:if(o=/4[0-9][0-9]/.test(e.code.toString()),e.eventSessionId!==this._ctx.publishSessionId)return[3,6];this._publishRtmp="",_="",s.label=1;case 1:return s.trys.push([1,4,,5]),o?[3,3]:[4,this.publish()];case 2:l=s.sent(),_=l.rtmp,s.label=3;case 3:return[3,5];case 4:return s.sent(),[3,5];case 5:return _?this._ctx.clientEmit(a.UPDATE_URL,{uid:this._ctx.userId,screen:!1,url:_}):this._ctx.clientEmit(a.STREAM_FAILED,{uid:this._ctx.userId,screen:!1,reason:e.message}),[3,12];case 6:if(i=this._ctx.getUserInfoByStreamId(e.streamId),c=r(this._ctx.getStreamInfoByStreamId(e.streamId),2),u=c[0],d=c[1],!i||!u||e.eventSessionId!==u.pcSessionId)return[3,12];_="",s.label=7;case 7:return s.trys.push([7,10,,11]),o?[3,9]:(this._ctx.updateSubscribeId(i.clientId,d,y()),[4,this.subscribe(i.clientId,{audio:null===(t=null==u?void 0:u.subOption)||void 0===t?void 0:t.audio,video:null===(n=null==u?void 0:u.subOption)||void 0===n?void 0:n.video,screen:d})]);case 8:l=s.sent(),_=l.rtmp,s.label=9;case 9:return[3,11];case 10:return s.sent(),[3,11];case 11:_?this._ctx.clientEmit(a.UPDATE_URL,{uid:i.clientId,screen:d,url:_}):(this._ctx.clientEmit(a.STREAM_FAILED,{uid:i.clientId,screen:d,reason:e.message}),d?delete i.screenStream:delete i.stream),s.label=12;case 12:return[2]}}))}))},o.prototype._onUpdateStreamAttributes=function(e){var t=e.attributes,o=e.streamUserId,i=e.screen,s=this._ctx.getUserInfo(o),r=this._ctx.getRemoteStreamInfo(o,i);if(s&&r){var c=t.audiostream,u=t.videostream,d=r.attributes,_=d.audiostream,l=d.videostream;A(c)&&A(_)&&c!==_&&(c?this._ctx.clientEmit(a.UNMUTE_AUDIO,{uid:s.clientId,screen:i}):this._ctx.clientEmit(a.MUTE_AUDIO,{uid:s.clientId,screen:i})),A(u)&&A(l)&&u!==l&&(u?this._ctx.clientEmit(a.UNMUTE_VIDEO,{uid:s.clientId,screen:i}):this._ctx.clientEmit(a.MUTE_VIDEO,{uid:s.clientId,screen:i})),r.attributes||(r.attributes={}),r.attributes=n(n({},r.attributes),t)}},o.prototype._onMessageReceived=function(e,t){var n,o={uid:e.userId,message:e.isBinary?S(e.msg):e.msg};if(e.isBinary||"user"!==t)if(e.isBinary&&"user"===t)n=a.USER_BINARY_MESSAGE_RECEIVED;else if(e.isBinary||"room"!==t){if(!e.isBinary||"room"!==t)throw"not reach here";n=a.ROOM_BINARY_MESSAGE_RECEIVED}else n=a.ROOM_MESSAGE_RECEIVED;else n=a.USER_MESSAGE_RECEIVED;this._ctx.clientEmit(n,o)},o.prototype._checkConnect=function(){if(!this._ctx.connection.isSignalingExist())throw{code:R.WEBSOCKET_NOT_CONNECTED,reason:"websocket not open, signaling not exist"}},o.prototype._sendSignalingWaitEvent=function(e,t,n){return void 0===n&&(n=0),i(this,void 0,void 0,(function(){var o,i,r,c,a,u;return s(this,(function(s){switch(s.label){case 0:o=t.msgId,i=b(),r=i.promise,c=i.resolve,a=i.reject,this._eventCallbackMap.set(o,{resolve:c,reject:a}),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._ctx.connection.sendSignaling(e,t,n)];case 2:return s.sent(),[3,4];case 3:return u=s.sent(),a(u),[3,4];case 4:return[2,r]}}))}))},Object.defineProperty(o.prototype,"state",{get:function(){return this._state},set:function(e){this._state=e},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"localRtmp",{get:function(){return this._publishRtmp},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"localStreamId",{get:function(){return this._publishStreamId},enumerable:!1,configurable:!0}),o}(u.EventEmitter),M={sendUserMessage:"@signaling/onUserMessageSendResult",sendRoomMessage:"@signaling/onRoomMessageSendResult"},w=["ws.rtc.volcvideo.com","ws-hl.rtc.volcvideo.com"],L=["common.rtc.volcvideo.com","common-hl.rtc.volcvideo.com"];function U(e,t){return new Promise((function(n,o){wx.request({url:e,data:t.body,method:t.method,header:{"content-type":"application/json"},success:function(e){n(e.data)},fail:o})}))}var k,P=function(e,t){return new Promise((function(n,o){var i=(e=Array.isArray(e)?e:[]).length,s=[],r=!1;0===i?o([]):e.forEach((function(e){e.then((function(e){r&&t&&t(e),r=!0,n(e)}),(function(e){i--,s.push(e),0===i&&o(s)}))}))}))},B=D.getDeviceId();function G(e,t){var n={event:t.event,streamID:t.streamID,streamAddr:t.streamAddr,userID:t.userID,roomID:t.roomID,deviceID:B,rtcSID:B,appID:t.appID,sdkVersion:"3.3.0",result:t.result,reason:t.reason,code:t.code};e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"start"+JSON.stringify(n)}),U("https://"+(t.logHost||"common.rtc.volcvideo.com")+"/dispatch/v1/Report?Action=UploadRTMPResult",{method:"POST",body:n}).then((function(){e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"success"})})).catch((function(t){e.report("rtc_invoke_status",{sdk_api_name:"UploadRTMPResult",message:"error"+JSON.stringify(t)})}))}function F(e){return void 0===e&&(e=0),function(t,n,o){var i=o.value;o.value=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var o=i.apply(this,t);return o.then((function(n){var o,i;return null===(i=null===(o=t[e])||void 0===o?void 0:o.call)||void 0===i||i.call(o,null,n),n})).catch((function(n){var o,i;throw null===(i=null===(o=t[e+1])||void 0===o?void 0:o.call)||void 0===i||i.call(o,null,n),n}))}}}function j(e){return new Promise((function(t){setTimeout((function(){t()}),e)}))}!function(e){e.init="init",e.connecting="connecting",e.connected="connected",e.reconnecting="reconnecting",e.reconnected="reconnected",e.disconnected="disconnected",e.failed="failed"}(k||(k={}));var V=function(e){function o(t,n){void 0===n&&(n=!1);var o=e.call(this)||this;return o._ctx=t,o._isReconnect=n,o._host="",o._pingInterval=5e3,o._pingTimeout=1e4,o._pingTimer=null,o._maxRetryTime=3,o._requestId=-1,o._cache=new Map,o._state=k.init,o.__test__1006=function(){o._socketTask.close({success:function(){o.emit("@signaling/onClose",{code:1006,reason:""}),o._state=k.disconnected}})},o._monitor=t.monitor,wx.__test__1006=o.__test__1006,o}return t(o,e),o.prototype.connect=function(e,t){var n=this;void 0===t&&(t="/rtmp_socket/");var o="wss://"+e+t;return this._host=e,this._ctx.updateSessionId(),new Promise((function(e,t){var i=y(),s=o+"?wsid="+i+"&appid="+n._ctx.appId+"&ua=miniapp-3.3.0&EIO=3&transport=websocket";n._reportStateChange(n._isReconnect?k.reconnecting:k.connecting,s);var r=wx.connectSocket({url:s});n._ctx.utEmitter.emit("startConnectWS",o),r.onError((function(e){t({code:R.WEBSOCKET_FAILED,reason:e.errMsg}),n._isDisconnect()||(n._reportStateChange(k.failed,JSON.stringify(e),e.code),n.emit("@signaling/error",{reason:e.errMsg,code:e.code||R.WEBSOCKET_FAILED}))})),r.onOpen((function(t){n._socketTask=r,n._ping(),n._reportStateChange(n._isReconnect?k.reconnected:k.connected,s),e(t)})),r.onClose((function(e){n._isDisconnect()||(n._reportStateChange(k.disconnected,JSON.stringify(e),e.code),n.emit("@signaling/onClose",e))})),r.onMessage((function(e){"string"==typeof(null==e?void 0:e.data)&&n._handleMessage(e.data)}))}))},o.prototype.disconnect=function(){var e=this;return new Promise((function(t,n){if(e._socketTask.readyState!==e._socketTask.OPEN)return t();e._socketTask.close({success:t,fail:n})}))},o.prototype.sendSignaling=function(e,t,n){return void 0===n&&(n=0),i(this,void 0,void 0,(function(){var o,i;return s(this,(function(s){switch(s.label){case 0:return s.trys.push([0,2,,6]),[4,this._sendSignaling(e,t)];case 1:return o=s.sent(),[3,6];case 2:return i=s.sent(),n<this._maxRetryTime&&/^5[0-9][0-9]$/.test(""+i.code)?[4,this.sendSignaling(e,t,n+1)]:[3,4];case 3:return o=s.sent(),[3,5];case 4:throw i;case 5:return[3,6];case 6:return[2,o]}}))}))},o.prototype._sendSignaling=function(e,t){var n=this;return new Promise((function(o,i){var s,r;if(n._socketTask&&(null===(s=n._socketTask)||void 0===s?void 0:s.readyState)===n._socketTask.OPEN){t.sessionId=n._ctx.rtcSessionId,t.timestamp=(new Date).valueOf();var c=++n._requestId,a="joinRoom"===e?60:10,u=setTimeout((function(){n._cache.delete(c),i({code:R.SIGNALING_TIMEOUT,message:e+" signaling timeout",reason:"timeout_"+a})}),1e3*a);n._cache.set(c,{startTs:Date.now(),signaling_event:e,success:function(e){clearTimeout(u),o(e)},error:function(e){clearTimeout(u),i(e)}});var d=[e,t];n._reportRtcSignaling({signaling_event:"call-"+e,direction:"up",message:t}),n._socketTask.send({data:"42"+n._requestId+JSON.stringify(d)})}else i({code:R.WEBSOCKET_NOT_CONNECTED,reason:"websocket not open, socketTask.state="+(null===(r=n._socketTask)||void 0===r?void 0:r.readyState)})}))},o.prototype._feedbackMessage=function(e,t){var n;if(this._socketTask&&(null===(n=this._socketTask)||void 0===n?void 0:n.readyState)===this._socketTask.OPEN&&"switchIDC"!==e){var o=[e,t];this._reportRtcSignaling({signaling_event:e,direction:"up",message:t}),this._socketTask.send({data:"42"+JSON.stringify(o)})}},o.prototype._ping=function(){var e,t=this;this._socketTask&&(null===(e=this._socketTask)||void 0===e?void 0:e.readyState)===this._socketTask.OPEN&&(this._socketTask.send({data:"2"}),this._pingTimer=setTimeout((function(){t._isDisconnect()||(t._reportStateChange(k.disconnected,"pong timeout",-1),t.emit("@signaling/onClose",{code:1006,reason:"pong timeout"}),t._socketTask.close({}))}),this._pingTimeout))},o.prototype._handleMessage=function(e){var t=this;if("3"===e)return this._clearPingTimer(),void setTimeout((function(){return t._ping()}),this._pingInterval);var o=this._parseMessage(e)||{},i=o.type,s=o.requestId,r=o.packetType,c=o.signalingType,a=o.payload,u=void 0===a?{}:a;if(0===i){var d=u.pingInterval,_=void 0===d?5e3:d,l=u.pingTimeout,h=void 0===l?1e4:l;return this._pingInterval=_,void(this._pingTimeout=h)}if(4===i)if(3===r){if(s){var p=this._cache.get(+s);this._reportRtcSignaling({signaling_event:c,direction:"down",message:u,code:u.code,elapse:Date.now()-((null==p?void 0:p.startTs)||Date.now())}),200===u.code?null==p||p.success(u):null==p||p.error({code:u.code,message:u.message,reason:"number"==typeof u.code?"signaling_error_"+u.code:"ack_failed",sessionId:u.sessionId,eventSessionId:u.eventSessionId}),this._cache.delete(+s)}}else 2===r&&(this._reportRtcSignaling(n({signaling_event:"on-"+c,direction:"down",message:u,code:(null==u?void 0:u.code)||200},"streamFailed"===c?{signaling_type:null==u?void 0:u.type}:{})),setTimeout((function(){t.emit("@signaling/"+c,u)})),c&&u&&this._feedbackMessage(c+"-res",{messageId:null==u?void 0:u.messageId}))},o.prototype._reportRtcSignaling=function(e){var t=n(n({},e),{signaling_server:this._host}),o=e.message,i=o.streamId,s=o.eventSessionId,c=o.userType,a=o.streamUserId,u=o.screen;if("string"==typeof i){t.stream_id=i;var d=this._ctx.getUserInfoByStreamId(i),_=r(this._ctx.getStreamInfoByStreamId(i),1)[0];d&&(t.peer_user_type=d.userType,t.stream_user_id=d.clientId),_&&(t.pc_session_id=_.pcSessionId)}if("string"==typeof a){t.stream_user_id=a;d=this._ctx.getUserInfo(a),_=this._ctx.getRemoteStreamInfo(a,!!u);d&&(t.peer_user_type=d.userType),_&&(t.pc_session_id=_.pcSessionId)}c&&(t.peer_user_type=c),s&&(t.pc_session_id=s),this._monitor.report("rtc_signaling",t)},o.prototype._reportStateChange=function(e,t,n){void 0===t&&(t=""),void 0===n&&(n=0),this._state=e,this._monitor.report("rtc_websocket",{error_code:n,signaling_server:this._host,message:t,signaling_event:e})},o.prototype._isDigitChar=function(e){return/\d/.test(e)},o.prototype._parseMessage=function(e){for(var t={type:-1,packetType:-1,requestId:"",payload:null,signalingType:""},n=0,o="",i=0;i<e.length;i++){var s=e[i];if(!this._isDigitChar(s)){n=i;break}0===i?t.type=+s:1===i?t.packetType=+s:o+=s}if(t.requestId=o,3===t.packetType){var r=this._cache.get(+o);r&&(t.signalingType=r.signaling_event)}try{var c=JSON.parse(e.substr(n));Array.isArray(c)?1===c.length?t.payload=c[0]:2===c.length&&(t.signalingType=c[0],t.payload=c[1]):t.payload=c}catch(e){}return t},o.prototype.destroy=function(t){this._monitor.report("rtc_invoke_status",{sdk_api_name:"signaling.destroy",message:t}),this._cache.forEach((function(e){e.error({code:R.WEBSOCKET_FAILED,message:t,reason:"leave_room"})})),this._cache.clear(),e.prototype.removeAllListeners.call(this),this._clearPingTimer(),this.disconnect().catch((function(){}))},o.prototype._clearPingTimer=function(){this._pingTimer&&(clearInterval(this._pingTimer),this._pingTimer=null)},o.prototype._isDisconnect=function(){return this._state===k.failed||this._state===k.disconnected},o}(u.EventEmitter),H=function(e){function n(t){var n=e.call(this)||this;return n._ctx=t,n._retryCount=0,n._processIDC=!1,n._reconnectFailed=!1,n._useByRoom=!1,n._useByRts=!1,n}return t(n,e),n.prototype.connect=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t,n=this;return s(this,(function(o){return this._connectionTask?[2,this._connectionTask.promise]:(t=b(),this._connectionTask=t,this._connect(e).then(t.resolve).catch((function(e){t.reject(e),delete n._connectionTask})),[2,t.promise])}))}))},n.prototype._connect=function(e){return i(this,void 0,void 0,(function(){var t;return s(this,(function(n){return(t=this._ctx.getSocketHost())?[2,this.doSocketConnect(t,e)]:[2,this.internalConnect(e)]}))}))},n.prototype.sendSignaling=function(e,t,n){return void 0===n&&(n=0),i(this,void 0,void 0,(function(){return s(this,(function(o){if(!this._signaling)throw{code:R.WEBSOCKET_NOT_CONNECTED,reason:"websocket not open, cannot send signaling: "+e};return[2,this._signaling.sendSignaling(e,t,n)]}))}))},n.prototype.resetRetryCount=function(){this._retryCount=0},n.prototype.isSignalingExist=function(){return!!this._signaling},n.prototype.addCustomer=function(e){"room"===e&&(this._useByRoom=!0),"rts"===e&&(this._useByRts=!0),K("addCustomer",e,this._ctx.monitor)},n.prototype.deleteCustomer=function(e){"room"===e&&(this._useByRoom=!1),"rts"===e&&(this._useByRts=!1),K("deleteCustomer",e,this._ctx.monitor),this._useByRoom||this._useByRts||this.destroy("no connection customer")},n.prototype.destroy=function(e){var t;this._closeSignaling(e),this.resetRetryCount(),this._reconnectTimer&&(clearTimeout(this._reconnectTimer),delete this._reconnectTimer),this._reconnectFailed=!1,null===(t=this._connectionTask)||void 0===t||t.reject("connection closed destroy"),delete this._connectionTask,this._useByRoom=!1,this._useByRts=!1},n.prototype.doSocketConnect=function(e,t){return void 0===t&&(t=!1),i(this,void 0,void 0,(function(){var n,o=this;return s(this,(function(i){return[2,(n=new V(this._ctx,t)).connect(e).then((function(t){return o._wsDomain=e,o._signaling=n,o._addSignalingEventHandler(),t}))]}))}))},n.prototype._addSignalingEventHandler=function(){var e,t,n,o=this;["@signaling/userConnection","@signaling/userDisconnection","@signaling/onAddStream","@signaling/onRemoveStream","@signaling/streamFailed","@signaling/onUpdateStreamAttributes","@signaling/onLogout","@signaling/onGetPeerOnlineStatus","@signaling/onUserMessageReceived","@signaling/onRoomMessageReceived","@signaling/onUserMessageReceivedOutsideRoom","@signaling/onUserMessageSendResult","@signaling/onRoomMessageSendResult","@signaling/onUserMessageSendResultOutsideRoom","@signaling/onServerMessageSendResult","@signaling/onServerParamsSetResult"].forEach((function(e){var t;null===(t=o._signaling)||void 0===t||t.on(e,(function(t){o.emit(e,t)}))})),null===(e=this._signaling)||void 0===e||e.on("@signaling/onClose",(function(e){1006!==e.code?(o._closeSignaling("websocket close"),o.emit("@connection/close",e)):o._handle1006(e)})),null===(t=this._signaling)||void 0===t||t.on("@signaling/switchIDC",this._handleSwitchIDC.bind(this)),null===(n=this._signaling)||void 0===n||n.on("@signaling/error",(function(e){o._closeSignaling("websocket close"),o.emit("@connection/error",e)}))},n.prototype.useCacheDomainsConnect=function(e,t){return void 0===t&&(t=!1),i(this,void 0,void 0,(function(){var n,o;return s(this,(function(i){switch(i.label){case 0:if(!(n=e.shift()))throw K("use_up","",this._ctx.monitor),{code:R.WEBSOCKET_FAILED,reason:"use cache domain connect fail"};i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.doSocketConnect(n,t)];case 2:return i.sent(),K("socketConnectSuccess","use cache, domain: "+n,this._ctx.monitor),[3,4];case 3:return o=i.sent(),K("socketConnectFail","use cache, domain: "+n+", error: "+(o.message||o.reason),this._ctx.monitor),this.emit("@test/cache_fail"),[2,this.useCacheDomainsConnect(e)];case 4:return[2]}}))}))},n.prototype.internalConnect=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t,n,o,i,r,c,a,u;return s(this,(function(s){switch(s.label){case 0:t=D.getCacheSync(C),n=this._ctx.getEnvDomains(),o=(null==n?void 0:n.configDomains)||L,this._getWsDomainAndCache(o).catch((function(){})),i=Date.now(),s.label=1;case 1:return s.trys.push([1,6,,9]),Array.isArray(t)&&t.length?(K("socketConnectWillUseCache",t.toString(),this._ctx.monitor),[4,this.useCacheDomainsConnect(t,e)]):[3,3];case 2:return s.sent(),[3,5];case 3:return K("socketConnectWillUseDomain",r=(null==n?void 0:n.wsDomain)||(Math.random()>.5?w[0]:w[1]),this._ctx.monitor),[4,this.doSocketConnect(r,e)];case 4:s.sent(),s.label=5;case 5:return[3,9];case 6:if(c=s.sent(),this._retryCount++>=this._ctx.connectMaxRetryCount())throw this._retryCount=0,K("socketConnectExceeded","will throw error, max retry count "+this._ctx.connectMaxRetryCount(),this._ctx.monitor),{code:R.WEBSOCKET_FAILED,reason:"retry count exceeded, max retry count "+this._ctx.connectMaxRetryCount()};return a=Date.now(),(u=a-i)<1e3?[4,j(1e3-u)]:[3,8];case 7:s.sent(),s.label=8;case 8:return K("socketConnectFailed_1","will retry "+c.reason||c.message,this._ctx.monitor),[2,this.internalConnect()];case 9:return[2]}}))}))},n.prototype._getWsDomainAndCache=function(e){return i(this,void 0,void 0,(function(){var t,n=this;return s(this,(function(o){return t=e.map((function(t){var o={appID:n._ctx.appId,roomID:n._ctx.roomId,keys:["multiDomain"],labelSelector:"serviceType=rtmp",sdkVersion:"3.3.0",deviceType:"miniapp",deviceId:D.getDeviceId()};return n._ctx.monitor.report("rtc_get_config",{error_code:0,host:e,type:"request",message:JSON.stringify(o)}),U("https://"+t+"/decision/v1/multi",{method:"POST",body:o})})),[2,P(t).then((function(e){void 0===e&&(e={});var t=e.multiDomain,o=e.domainBlacklist;n._ctx.monitor.report("rtc_get_config",{error_code:0,message:JSON.stringify(e),type:"response"});var i=[];return Array.isArray(t)&&(i=t.map((function(e){return e.host}))),D.setCacheSync(C,i),Array.isArray(o)&&o.find((function(e){return e===n._wsDomain}))&&n.emit("@connection/close",{code:R.DOMAIN_IN_BLACKLIST,reason:"current websocket domain "+n._wsDomain+" in blacklist"}),i})).catch((function(e){var t,o;n._ctx.monitor.report("rtc_get_config",{error_code:null===(t=e[0])||void 0===t?void 0:t.errno,message:null===(o=e[0])||void 0===o?void 0:o.errMsg,type:"response"})}))]}))}))},n.prototype._handle1006=function(e){var t=this;this._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"1006",message:e.reason}),this._closeSignaling("websocket close: 1006"),this._processIDC?console.warn("process idc"):this._reconnectTimer=setTimeout((function(){t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"start"}),t.emit("@connection/disconnected"),t._connect(!0).then((function(){delete t._reconnectTimer,t.emit("@connection/reconnected",{reason:"1006"}),t._reconnectFailed=!1,t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"success"})})).catch((function(e){delete t._reconnectTimer,t._reconnectFailed=!0,t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect",message:"failed"}),t._closeSignaling("websocket close"),t.emit("@connection/close",e)}))}),this._reconnectFailed?3e3:100)},n.prototype._handleSwitchIDC=function(e){var t=this;this._processIDC=!0,this._closeSignaling("websocket close: switchIDC"),D.removeStorage(C),this.doSocketConnect(e.target,!0).then((function(){t._processIDC=!1,t.emit("@connection/reconnected",{reason:"switchIDC"}),t._reconnectFailed=!1,t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect(idc 1)",message:"success"})})).catch((function(e){t._reconnectFailed=!0,t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect(idc 1)",message:"failed"+e.reason}),t.emit("@connection/disconnected"),t._connect(!0).then((function(){t._processIDC=!1,t.emit("@connection/reconnected",{reason:"switchIDC"}),t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect(idc 2)",message:"success"})})).catch((function(e){t._processIDC=!1,t._ctx.monitor.report("rtc_invoke_status",{sdk_api_name:"socket_reconnect(idc 2)",message:"failed"}),t._closeSignaling("websocket close"),t.emit("@connection/close",e)}))}))},n.prototype._closeSignaling=function(e){var t;null===(t=this._signaling)||void 0===t||t.destroy(e),delete this._signaling},n}(u.EventEmitter);function K(e,t,n){n.report("rtc_invoke_status",{sdk_api_name:"connection."+e,message:t})}var W=function(){function e(){this._destroyed=!1,this._header={product_line:"rtc",report_version:"5",rtc_sdk_version:"3.3.0",device_id:D.getDeviceId(),engine_session_id:y(),rtc_app_id:"",rtc_business_id:"",room_id:"",user_id:"",report_time:Date.now(),report_log_count:0,access:""},this._data=[],this._reportId=0,this._send2server()}return e.prototype.updateHeader=function(e){this._header=n(n({},this._header),e)},e.prototype.report=function(e,t){"rtc_media_statistics"!==e&&console.log("monitor> ",e,t,this._header);var o=Date.now();this._data.push(n(n({event_key:e},t),{message:JSON.stringify(t.message||{}),event_type:"event",report_id:this._reportId++,time:o,rtc_time:o}))},e.prototype.destroy=function(){this._destroyed=!0},e.prototype._send2server=function(){var e=this;if(this._data.length){var t=this._data.splice(0,200);this._header.report_time=Date.now(),this._header.report_log_count=t.length,this._header.access=J.networkType,U("https://log.snssdk.com/video/v1/webrtc_log/",{method:"POST",body:{data:t,header:this._header,os:"miniapp",from:"miniapp",version:"3"}}).catch((function(){e._data=[].concat(t,e._data)}))}else if(this._destroyed)return;setTimeout(this._send2server.bind(this),5e3)},e}(),J=function(){function e(t){var n;this._options=t,this.appId="",this.businessId="",this.roomId="",this.userId="",this.token="",this.publishSessionId="",this.monitor=new W,this.rtcSessionId="",this._role="normalUser",this._userMap=new Map,this.utEmitter=new u.EventEmitter,this.monitor.updateHeader({os_version:(null===(n=e.systemInfo)||void 0===n?void 0:n.version)||"",user_agent:this.getUA()}),this.connection=new H(this),this._bindConnectionEvents()}return e.initWxEnv=function(){e.systemInfo=wx.getSystemInfoSync(),wx.getNetworkType({success:function(t){e.networkType=t.networkType}}),wx.onNetworkStatusChange((function(t){e.networkType=t.networkType}))},e.prototype.getUA=function(){var t=e.systemInfo||{},n=t.system,o=t.model,i=t.platform,s=t.brand;return JSON.stringify({system:n,model:o,platform:i,brand:s})},e.prototype.getLogHost=function(){return this._options.logHost},e.prototype.getSocketHost=function(){return this._options.sshost},e.prototype.getServers=function(){return this._options.servers},e.prototype.getEnvDomains=function(){return this._options.envDomains},e.prototype.connectMaxRetryCount=function(){return this._options.connectMaxRetryCount||5},e.prototype.getUserAgentIP=function(){return this._options._userAgentIP},e.prototype.getLambdaAddr=function(){return this._options._lambdaAddr},e.prototype.getMsAddr=function(){return this._options._expectedMSAddr},e.prototype.getRole=function(){return this._role},e.prototype.setRole=function(e){this._role=e},e.prototype.getUserInfo=function(e){return this._userMap.get(e)},e.prototype.getRemoteStreamInfo=function(e,t){var n=this.getUserInfo(e);return t?null==n?void 0:n.screenStream:null==n?void 0:n.stream},e.prototype.updateSubscribeId=function(e,t,n){var o=this.getRemoteStreamInfo(e,t);o&&(o.pcSessionId=n)},e.prototype.getSubscribeId=function(e,t){var n;return(null===(n=this.getRemoteStreamInfo(e,t))||void 0===n?void 0:n.pcSessionId)||""},e.prototype.getRemoteUserType=function(e){var t;return(null===(t=this.getUserInfo(e))||void 0===t?void 0:t.userType)||""},e.prototype.getRemoteStreamId=function(e,t){var n;return null===(n=this.getRemoteStreamInfo(e,t))||void 0===n?void 0:n.streamId},e.prototype.addRemoteUser=function(e){this._userMap.set(e.clientId,n({},e))},e.prototype.removeRemoteUser=function(e){this._userMap.delete(e)},e.prototype.getRomoteUserMap=function(){return this._userMap},e.prototype.getUserInfoByStreamId=function(e){var t;return this._userMap.forEach((function(n){var o,i;(null===(o=n.stream)||void 0===o?void 0:o.streamId)!==e&&(null===(i=n.screenStream)||void 0===i?void 0:i.streamId)!==e||(t=n)})),t},e.prototype.getStreamInfoByStreamId=function(e){var t,n=!1;return this._userMap.forEach((function(o){var i,s;(null===(i=o.stream)||void 0===i?void 0:i.streamId)===e&&(t=o.stream),(null===(s=o.screenStream)||void 0===s?void 0:s.streamId)===e&&(t=o.screenStream,n=!0)})),[t,n]},e.prototype.updateSessionId=function(){this.rtcSessionId=y(),this.monitor.updateHeader({rtc_session_id:this.rtcSessionId})},e.prototype.setEmit=function(e){this._emit=e},e.prototype.clientEmit=function(e,t){var n=this;this.monitor.report("rtc_sdk_callback",{sdk_callback_name:e,error_code:0,message:t}),setTimeout((function(){n._emit.call(null,e,t)}))},e.prototype.clearClientInfo=function(){this.appId="",this.clearRoomInfo(),this.utEmitter.removeAllListeners()},e.prototype.clearRoomInfo=function(){this.roomId="",this.userId="",this._role="normalUser",this.publishSessionId="",this._userMap.clear()},e.prototype._bindConnectionEvents=function(){var e=this;this.connection.on("@connection/close",(function(t){t.code===R.DOMAIN_IN_BLACKLIST?e.clientEmit(a.CLOSE,t):e.clientEmit(a.DISCONNECT,t)}))},e.networkType="unknown",e}();J.initWxEnv();var q,Y,X={1001:"推流：已经连接推流服务器",1002:"推流：已经与服务器握手完毕，开始推流",1003:"推流：打开摄像头成功",1004:"推流：录屏启动成功",1005:"推流：推流动态调整分辨率",1006:"推流：推流动态调整码率",1007:"推流：首帧画面采集完成",1008:"推流：编码器启动",1018:"推流：进房成功（ROOM协议特有）",1019:"推流：退房成功（ROOM协议特有有）",1020:"推流：远端主播列表变化（ROOM协议特有）",1021:"推流：网络变更时重进房，WiFi 切换到4G 会触发断线重连（ROOM协议特有）",1022:"推流：进入房间失败（ROOM协议特有）",1031:"推流：远端主播进房通知（ROOM协议特有）",1032:"推流：远端主播退房通知（ROOM协议特有）",1033:"推流：远端主播视频状态位变化（RROOM协议特有）",1034:"推流：远端主播音频状态位变化（ROOM协议特有）",1101:"推流：网络状况不佳：上行带宽太小，上传数据受阻",1102:"推流：网络断连, 已启动自动重连",1103:"推流：硬编码启动失败, 采用软编码",1104:"推流：视频编码失败, 内部会重启编码器",2001:"拉流：已经连接服务器",2002:"拉流：已经连接 RTMP 服务器,开始拉流",2003:"拉流：网络接收到首个视频数据包(IDR)",2004:"拉流：视频播放开始",2005:"拉流：视频播放进度",2006:"拉流：视频播放结束",2007:"拉流：视频播放Loading",2008:"拉流：解码器启动",2009:"拉流：视频分辨率改变",2030:"音频设备发生改变，即当前的输入输出设备发生改变，比如耳机被拔出",2032:"拉流：视频渲染首帧事件",2101:"拉流：当前视频帧解码失败",2102:"拉流：当前音频帧解码失败",2103:"拉流：网络断连, 已启动自动重连",2104:"拉流：网络来包不稳：可能是下行带宽不足，或由于主播端出流不均匀",2105:"拉流：当前视频播放出现卡顿",2106:"拉流：硬解启动失败，采用软解",2107:"拉流：当前视频帧不连续，可能丢帧",2108:"拉流：当前流硬解第一个I帧失败，SDK自动切软解",3001:"RTMP DNS解析失败",3002:"RTMP服务器连接失败",3003:"RTMP服务器握手失败",3004:"RTMP服务器主动断开，请检查推流地址的合法性或防盗链有效期",3005:"RTMP 读/写失败",4998:"Mic状态切换的时候，enable-mic触发(iOS特有)",4999:"mute状态切换的时候，muted 触发(iOS特有)",5001:"系统电话打断或者微信音视频电话打断",10001:"用户禁止使用摄像头",10002:"用户禁止使用录音",10003:"背景音资源（BGM）加载失败",10004:"等待画面资源（waiting-image）加载失败","-1301":"推流：打开摄像头失败","-1302":"推流：打开麦克风失败","-1303":"推流：视频编码失败","-1304":"推流：音频编码失败","-1305":"推流：不支持的视频分辨率","-1306":"推流：不支持的音频采样率","-1307":"推流：网络断连，且经多次重连抢救无效，更多重试请自行重启推流","-1308":"推流：开始录屏失败，可能是被用户拒绝","-1309":"推流：录屏失败，不支持的Android系统版本，需要5.0以上的系统","-1310":"推流：录屏被其他应用打断了","-1311":"推流：Android Mic打开成功，但是录不到音频数据","-1312":"推流：录屏动态切横竖屏失败",0:"无错误"},z="PREV_ENV_DOMAINS";function Q(e){return"string"==typeof e&&/^[a-zA-Z0-9@._\-]{1,128}$/.test(e)}!function(e){e[e.OFFLINE=0]="OFFLINE",e[e.ONLINE=1]="ONLINE",e[e.UNREACHABLE=2]="UNREACHABLE"}(q||(q={})),function(e){e.Connecting="connecting",e.Connected="connected",e.Disconnected="disconnected",e.Reconnecting="reconnecting"}(Y||(Y={}));var Z=function(){function e(e){this._state=Y.Disconnected,this._waitLoginToken=!1,this._eventCallbackMap=new Map,this._peerStatusRequestMap=new Map,this._ctx=e}return e.prototype.login=function(e){var t=this;if(this._sessionId)throw{code:R.LOGIN_FAILED,reason:"Already logined"};if(this._loginResolve)throw{code:R.LOGIN_FAILED,reason:"Is logging in, please try again later."};this._userId=e.userId,this._token=e.token;var n=b(),o=n.promise,i=n.resolve,s=n.reject;return this._loginResolve=i,this._loginReject=s,this._state=Y.Connecting,this._ctx.connection.connect().then((function(){return t._ctx.connection.addCustomer("rts"),t._addSignalingEventHandler(),t._login()})).then((function(){if(t._serverParam)return t.setServerParams(t._serverParam)})).catch((function(e){t._state=Y.Disconnected,"function"==typeof t._loginReject&&t._loginReject(e),delete t._loginResolve,delete t._loginReject})),o},e.prototype._login=function(){return i(this,void 0,void 0,(function(){var e,t,n,o,i;return s(this,(function(s){switch(s.label){case 0:if(!this._userId||!this._token)return[2];e=y(),t=y(),n={sessionId:e,eventSessionId:t,Authorization:$(this._token),_userAgentIP:this._ctx.getUserAgentIP()||"",_lambdaAddr:this._ctx.getLambdaAddr()||"",params:{appId:this._ctx.appId,businessId:this._ctx.businessId,sdkVersion:"3.3.0",deviceId:D.getDeviceId(),rtcSid:"",rtsUserId:this._userId}},s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._ctx.connection.sendSignaling("login",n)];case 2:return s.sent(),this._waitLoginToken=!1,this._state=Y.Connected,[3,4];case 3:return o=s.sent(),this._state=Y.Disconnected,i=this._handleLoginError(o),"function"==typeof this._loginReject&&this._loginReject(i),delete this._loginResolve,delete this._loginReject,[3,4];case 4:return"function"==typeof this._loginResolve&&this._loginResolve(),delete this._loginResolve,delete this._loginReject,this._sessionId=e,this._ctx.monitor.updateHeader({rtc_session_id:this._sessionId}),[2]}}))}))},e.prototype._handleLoginError=function(e){var t;return this._waitLoginToken=!1,e.sessionId?e.code===p.INVALID_TOKEN?(this._waitLoginToken=!0,{code:R.LOGIN_ERROR,reason:"Invalid Token"}):e.code===p.INVALID_USERID?{code:R.LOGIN_ERROR,reason:"Invalid User"}:(this._ctx.monitor.report("rtc_error",{error_code:R.LOGIN_ERROR,message:"["+e.code+"] "+e.message}),{code:R.LOGIN_ERROR,reason:e.message}):(this._ctx.monitor.report("rtc_error",{error_code:R.LOGIN_ERROR,message:null!==(t=e.message)&&void 0!==t?t:"no message"}),{code:R.LOGIN_ERROR,reason:"get unexpected message"})},e.prototype.logout=function(){return i(this,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:if(this._state===Y.Disconnected)return[2];if(!this._sessionId||!this._userId||!this._token)throw{code:R.NOT_LOGIN,reason:"not login"};return e={sessionId:this._sessionId,Authorization:$(this._token)},[4,this._ctx.connection.sendSignaling("logout",e).catch((function(){}))];case 1:return t.sent(),this._clearState(),this._ctx.connection.deleteCustomer("rts"),[2]}}))}))},e.prototype.updateLoginToken=function(e){return i(this,void 0,void 0,(function(){var t,n,o,i;return s(this,(function(s){return this._token=e,this._waitLoginToken?(t=b(),n=t.promise,o=t.resolve,i=t.reject,this._loginResolve=o,this._loginReject=i,this._login(),[2,n]):[2]}))}))},e.prototype.getPeerOnlineStatus=function(e){return i(this,void 0,void 0,(function(){var t,n;return s(this,(function(o){switch(o.label){case 0:if(!this._sessionId||!this._userId)throw{code:R.NOT_LOGIN,reason:"not login"};return t={sessionId:this._sessionId,eventSessionId:y(),peerUserId:e},(n=this._peerStatusRequestMap.get(e))||(n=b()),this._peerStatusRequestMap.set(e,n),[4,this._ctx.connection.sendSignaling("getPeerOnlineStatus",t)];case 1:return o.sent(),[4,n.promise];case 2:return[2,o.sent().status]}}))}))},e.prototype.sendUserMessageOutsideRoom=function(e){return i(this,void 0,void 0,(function(){var t,n,o;return s(this,(function(i){switch(i.label){case 0:if(!this._sessionId||!this._userId)throw{code:R.NOT_LOGIN,reason:"not login"};return t=e.message instanceof ArrayBuffer,e.message instanceof ArrayBuffer&&(e.message=E(e.message)),n={sessionId:this._sessionId,userId:e.peerUserId,eventSessionId:y(),msgId:y(),isBinary:t,msg:e.message},[4,this._sendSignalingWaitEvent("sendUserMessageOutsideRoom",n)];case 1:if((o=i.sent()).code!==g.SUCCESS)throw{code:R.SEND_MESSAGE_FAILED,reason:(O[o.code]||"unknown")+"(code="+o.code+")"};return[2]}}))}))},e.prototype.setServerParams=function(e){return i(this,void 0,void 0,(function(){var t;return s(this,(function(o){switch(o.label){case 0:if(!this._sessionId||!this._userId)throw{code:R.NOT_LOGIN,reason:"not login"};return t={sessionId:this._sessionId,eventSessionId:y(),signature:e.signature,url:e.url},[4,this._ctx.connection.sendSignaling("setServerParams",t)];case 1:return o.sent(),this._serverParam=n({},e),[2]}}))}))},e.prototype.sendServerMessage=function(e){return i(this,void 0,void 0,(function(){var t,n,o;return s(this,(function(i){switch(i.label){case 0:if(!this._sessionId||!this._userId)throw{code:R.NOT_LOGIN,reason:"not login"};if(!this._serverParam)throw{code:R.NO_SERVER_PARAM,reason:"no server parameters"};return t=e.message instanceof ArrayBuffer,e.message instanceof ArrayBuffer&&(e.message=E(e.message)),n={sessionId:this._sessionId,eventSessionId:y(),msgId:y(),isBinary:t,msg:e.message},[4,this._sendSignalingWaitEvent("sendServerMessage",n)];case 1:if((o=i.sent()).code!==g.SUCCESS)throw{code:R.SEND_MESSAGE_FAILED,reason:(O[o.code]||"unknown")+"(code="+o.code+")"};return[2]}}))}))},e.prototype.destroy=function(){this.logout().catch((function(){})),this._eventCallbackMap.forEach((function(e){e.reject({code:R.NOT_LOGIN})})),this._eventCallbackMap.clear()},e.prototype._addSignalingEventHandler=function(){var e=this,t=function(){e._state=Y.Disconnected,e._ctx.connection.deleteCustomer("rts")},n=function(t){var n=e._peerStatusRequestMap.get(t.peerUserId);n&&(n.resolve(t),e._peerStatusRequestMap.delete(t.peerUserId))},o=function(t){var n,o={uid:t.userId,message:t.isBinary?S(t.msg):t.msg};n=t.isBinary?a.USER_BINARY_MESSAGE_RECEIVED_OUTSIDE_ROOM:a.USER_MESSAGE_RECEIVED_OUTSIDE_ROOM,e._ctx.clientEmit(n,o)},i=function(t){e._ctx.clientEmit(a.SERVER_PARAMS_SET_RESULT,t)},s=function(t){t.reason===f.DUPLICATE_LOGIN&&(e._ctx.clientEmit(a.LOGIN_BANNED),e._clearState(),e._ctx.connection.deleteCustomer("rts"))},r=function(t){var n=t.msgId,o=e._eventCallbackMap.get(n);o&&o.resolve(t)};this._ctx.connection.on("@connection/close",t),this._ctx.connection.on("@connection/disconnected",t),this._ctx.connection.on("@connection/reconnected",(function(){e._login().catch((function(){e._ctx.clientEmit(a.LOGIN_BANNED),e._clearState(),e._ctx.connection.deleteCustomer("rts")}))})),this._ctx.connection.on("@connection/error",t),this._ctx.connection.on("@signaling/onGetPeerOnlineStatus",n),this._ctx.connection.on("@signaling/onUserMessageReceivedOutsideRoom",o),this._ctx.connection.on("@signaling/onServerParamsSetResult",i),this._ctx.connection.on("@signaling/onLogout",s);var c=Object.values(ee);c.forEach((function(t){return e._ctx.connection.on(t,r)})),this._clearListeners=function(){e._ctx.connection.off("@signaling/onGetPeerOnlineStatus",n),e._ctx.connection.off("@signaling/onUserMessageReceivedOutsideRoom",o),e._ctx.connection.off("@signaling/onServerParamsSetResult",i),e._ctx.connection.off("@signaling/onLogout",s),c.forEach((function(t){return e._ctx.connection.off(t,r)}))}},e.prototype._sendSignalingWaitEvent=function(e,t,n){return void 0===n&&(n=0),i(this,void 0,void 0,(function(){var o,i,r,c,a,u;return s(this,(function(s){switch(s.label){case 0:o=t.msgId,i=b(),r=i.promise,c=i.resolve,a=i.reject,this._eventCallbackMap.set(o,{resolve:c,reject:a}),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._ctx.connection.sendSignaling(e,t,n)];case 2:return s.sent(),[3,4];case 3:return u=s.sent(),a(u),[3,4];case 4:return[2,r]}}))}))},e.prototype._clearState=function(){var e;delete this._userId,delete this._token,delete this._sessionId,this._ctx.monitor.updateHeader({rtc_session_id:""}),null===(e=this._clearListeners)||void 0===e||e.call(this),delete this._clearListeners,this._state=Y.Disconnected},e}();function $(e){return e.startsWith("Basic")?e:"Bearer "+e}var ee={sendUserMessageOutsideRoom:"@signaling/onUserMessageSendResultOutsideRoom",sendServerMessage:"@signaling/onServerMessageSendResult"};function te(){return function(e,t,n){if("function"==typeof n.value){var o=n.value;n.value=function(){for(var e,n,i=this,s=[],r=0;r<arguments.length;r++)s[r]=arguments[r];null===(e=this._monitor)||void 0===e||e.report("rtc_sdk_api_call",{sdk_api_name:t,message:s.filter((function(e){return"function"!=typeof e}))});var c=o.apply(this,s);return"function"==typeof(null==c?void 0:c.then)?c.then((function(e){var n;return null===(n=i._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:0,message:JSON.stringify(e||{})}),e})).catch((function(e){var n;throw null===(n=i._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:e.code,message:JSON.stringify(e.message||e.reason)}),e})):(null===(n=this._monitor)||void 0===n||n.report("rtc_sdk_callback",{sdk_callback_name:t,error_code:0,message:JSON.stringify(c||{})}),c)}}}}return{Client:function(e){function r(t){void 0===t&&(t={});var n=e.call(this)||this;return n.clsName="Client",n._inited=!1,n._reported={},function(e){void 0===e&&(e={});try{if(e.envDomains&&(!e.envDomains.wsDomain||!e.envDomains.configDomains))return console.warn("[rtc sdk] invalid envDomains, then envDomains will be ignored"),delete e.envDomains,1;var t=D.getCacheSync(z);(e.envDomains||t)&&(e.envDomains?t?(e.envDomains.wsDomain!==t.wsDomain||e.envDomains.configDomains.toString()!==t.configDomains.toString()?(console.warn("[rtc sdk] change env, will clear pre ws domain cache"),D.removeStorageSync(C)):console.warn("[rtc sdk] env same with prev"),D.setCacheSync(z,e.envDomains)):(D.removeStorageSync(C),D.setCacheSync(z,e.envDomains)):(D.removeStorageSync(C),D.removeStorageSync(z)))}catch(e){return console.warn("[rtc sdk] handle env domains error",e),6}}(t),n._ctx=new J(t),n._monitor=n._ctx.monitor,wx.__client=n,n._monitor.report("rtc_sdk_api_call",{sdk_api_name:"constructor",message:t}),n._ctx.setEmit(n.emit.bind(n)),n}return t(r,e),r.prototype.init=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!e||"string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"appId must be a string"};if(this._inited)throw{code:R.HAS_INITED,reason:"Has been initialized"};return this._ctx.appId=e,this._inited=!0,this._monitor.updateHeader({rtc_app_id:e}),[2]}))}))},r.prototype.destroy=function(){for(var e,t,n,o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return i(this,void 0,void 0,(function(){return s(this,(function(o){switch(o.label){case 0:return(null===(e=this._room)||void 0===e?void 0:e.state)!==N.Connected?[3,2]:[4,this.leave().catch((function(){}))];case 1:o.sent(),o.label=2;case 2:this._room&&this._room.removeAllListeners("@room/"+a.CLIENT_BANNED);try{null===(t=this._room)||void 0===t||t.destroy(),null===(n=this._rtsClient)||void 0===n||n.destroy(),this._ctx.connection.destroy("engine destroy"),this._inited=!1,this._ctx.clearClientInfo(),delete this._room,this._reported={}}catch(e){}return this._monitor.destroy(),[2]}}))}))},r.prototype.setBusinessId=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!e||!Q(e))throw{code:R.INVALID_PARAMS,reason:"businessId must be a string"};if(this._room)throw{code:R.ALREADY_IN_ROOM,reason:"already in room"};return this._monitor.updateHeader({rtc_business_id:e}),this._ctx.businessId=e,[2]}))}))},r.prototype.setUserVisibility=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){var t;return s(this,(function(n){switch(n.label){case 0:if(t=e?"normalUser":"silentUser",this._ctx.getRole()===t)return[2];if(!this._room)return this._ctx.setRole(t),[2];if(this._room.state===N.Connecting||this._room.state===N.Reconnecting)throw{code:R.CONNECTING,reason:"connecting"};return!e?[4,this._room.unpublish().catch((function(){}))]:[3,2];case 1:n.sent(),n.label=2;case 2:return[4,this._room.updateUserAttributes({role:t})];case 3:return n.sent(),this._ctx.setRole(t),[2]}}))}))},r.prototype.join=function(e,t,n){for(var o,r,c,a=[],u=3;u<arguments.length;u++)a[u-3]=arguments[u];return i(this,void 0,void 0,(function(){var i,a,u,d,_,l=this;return s(this,(function(s){switch(s.label){case 0:if(!t||"string"!=typeof t||!n||"string"!=typeof n)throw{code:R.INVALID_PARAMS,reason:"roomId: "+t+" or userId: "+n+" must be a string"};if("string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"token: "+e+" must be a string"};if((null===(o=this._room)||void 0===o?void 0:o.state)===N.Connecting||(null===(r=this._room)||void 0===r?void 0:r.state)===N.Reconnecting)throw{code:R.CONNECTING,reason:"connecting"};if((null===(c=this._room)||void 0===c?void 0:c.state)===N.Connected)throw{code:R.ALREADY_IN_ROOM,reason:"you are already in room"};i=Date.now(),this._joinRoomStartTs=i,this._monitor.report("join_room",{type:"begin",start:i}),this._monitor.updateHeader({rtc_app_id:this._ctx.appId,room_id:t,user_id:n}),this._room=new x({ctx:this._ctx}),this._handleRoom(),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._room.connect()];case 2:return s.sent(),[3,4];case 3:throw a=s.sent(),this._monitor.report("join_room",{type:"end",result:!1,reason:"connect_failed",start:i,elapse:Date.now()-i}),delete this._joinRoomStartTs,a;case 4:return[4,j(0)];case 5:s.sent(),s.label=6;case 6:if(s.trys.push([6,8,,9]),!this._room)throw{code:R.JOIN_ROOM_ERROR,reason:"leave_room",message:"call leaveRoom"};return this._ctx.roomId=t,this._ctx.userId=n,this._ctx.token=e,u=setTimeout((function(){l._monitor.report("join_room",{type:"end",result:!1,start:i,elapse:Date.now()-i,reason:"timeout_10"})}),1e4),[4,this._room.join()];case 7:return d=s.sent(),this._monitor.report("join_room",{type:"end",result:!0,start:i,elapse:Date.now()-i}),delete this._joinRoomStartTs,u&&clearTimeout(u),[2,d.clientId];case 8:throw _=s.sent(),this._monitor.report("join_room",{type:"end",result:!1,start:i,elapse:Date.now()-i,reason:_.reason}),delete this._joinRoomStartTs,u&&clearTimeout(u),_;case 9:return[2]}}))}))},r.prototype.leave=function(){for(var e,t,n,o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return i(this,void 0,void 0,(function(){return s(this,(function(o){switch(o.label){case 0:return(null===(e=this._room)||void 0===e?void 0:e.localRtmp)&&this.unpublish().catch((function(){})),[4,null===(t=this._room)||void 0===t?void 0:t.leave().catch((function(){}))];case 1:return o.sent(),null===(n=this._room)||void 0===n||n.destroy(),delete this._room,this._ctx.roomId="",this._ctx.userId="",this._ctx.token="",this._joinRoomStartTs&&this._monitor.report("join_room",{type:"end",result:!1,reason:"leave_room",start:this._joinRoomStartTs,elapse:Date.now()-this._joinRoomStartTs}),[2]}}))}))},r.prototype.publish=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i(this,void 0,void 0,(function(){var e,t,n,o,i,r;return s(this,(function(s){switch(s.label){case 0:if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};if("silentUser"===this._ctx.getRole())throw{code:R.PUBLISH_ERROR,reason:"no publish permission"};e=Date.now(),t=y(),this._ctx.publishSessionId=t,s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._room.publish()];case 2:return n=s.sent(),o=n.rtmp,i=n.streamId,this._monitor.report("rtc_publish",{start:e,elapse:Date.now()-e,stream_id:i,pc_session_id:t}),[2,o];case 3:throw r=s.sent(),this._monitor.report("rtc_publish_fail",{start:e,elapse:Date.now()-e,reason:r.reason,pc_session_id:t}),r;case 4:return[2]}}))}))},r.prototype.unpublish=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[4,this._room.unpublish()];case 1:return e.sent(),this._ctx.publishSessionId="",[2]}}))}))},r.prototype.subscribe=function(e,t){for(var o=[],r=2;r<arguments.length;r++)o[r-2]=arguments[r];return i(this,void 0,void 0,(function(){var o,i,r,c,a,u;return s(this,(function(s){switch(s.label){case 0:if(t=n(n({},{audio:!0,video:!0,screen:!1}),t),"string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"userId: "+e+" must be a string"};if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};o=Date.now(),i=y(),this._ctx.updateSubscribeId(e,t.screen,i),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this._room.subscribe(e,t)];case 2:return r=s.sent(),c=r.rtmp,a=r.streamId,this._monitor.report("rtc_subscribe",{start:o,elapse:Date.now()-o,stream_user_id:e,stream_id:a,peer_user_type:this._ctx.getRemoteUserType(e),pc_session_id:i}),[2,c];case 3:throw u=s.sent(),this._monitor.report("rtc_subscribe_fail",{start:o,reason:u.reason,elapse:Date.now()-o,stream_user_id:e,peer_user_type:this._ctx.getRemoteUserType(e),pc_session_id:i}),u;case 4:return[2]}}))}))},r.prototype.unsubscribe=function(e,t){for(var o=[],r=2;r<arguments.length;r++)o[r-2]=arguments[r];return i(this,void 0,void 0,(function(){return s(this,(function(o){switch(o.label){case 0:if(t=n(n({},{screen:!1}),t),"string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"userId: "+e+" must be a string"};if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[4,this._room.unsubscribe(e,t)];case 1:return o.sent(),this._ctx.updateSubscribeId(e,t.screen,""),[2]}}))}))},r.prototype.muteLocal=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[4,this._room.muteLocal(e)];case 1:return[2,t.sent()]}}))}))},r.prototype.muteRemote=function(e,t){for(var o=[],r=2;r<arguments.length;r++)o[r-2]=arguments[r];return i(this,void 0,void 0,(function(){return s(this,(function(o){switch(o.label){case 0:if(t=n(n({},{screen:!1}),t),!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};if(!e||"string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"userId: "+e+" must be a string"};if("boolean"!=typeof(null==t?void 0:t.audio)&&"boolean"!=typeof(null==t?void 0:t.video))throw{code:R.INVALID_PARAMS,reason:"options.audio or audio.video must be present"};return[4,this._room.muteRemote(e,t)];case 1:return[2,o.sent()]}}))}))},r.prototype._handleRoom=function(){var e=this;this._room&&this._room.on("@room/"+a.CLIENT_BANNED,(function(){var t;null===(t=e._room)||void 0===t||t.destroy()}))},r.prototype.reportPusherStateChange=function(e,t){var n,o,i,s,r,c,a,u=X[e];if(u&&(t+="("+u+")"),this._monitor.report("rtc_media_statistics",{direction:"up",code:e,message:t,stream_id:null===(n=this._room)||void 0===n?void 0:n.getLocalStreamId(),sdk_api_name:"pusherStateChange",pc_session_id:this._ctx.publishSessionId}),1008===e){var d=this._ctx.userId+"_"+(null===(o=null==this?void 0:this._room)||void 0===o?void 0:o.localStreamId)+"_success";if(this._reported[d])return;this._reported[d]=!0,G(this._monitor,{event:"publish",streamID:(null===(i=this._room)||void 0===i?void 0:i.localStreamId)||"",streamAddr:(null===(s=this._room)||void 0===s?void 0:s.localRtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"success",reason:t,code:e,logHost:this._ctx.getLogHost()})}else if(-1307===e){d=this._ctx.userId+"_"+(null===(r=null==this?void 0:this._room)||void 0===r?void 0:r.localStreamId)+"_failed";if(this._reported[d])return;G(this._monitor,{event:"publish",streamID:(null===(c=this._room)||void 0===c?void 0:c.localStreamId)||"",streamAddr:(null===(a=this._room)||void 0===a?void 0:a.localRtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"failed",reason:t,code:e,logHost:this._ctx.getLogHost()})}},r.prototype.reportPusherNetStatusChange=function(e){var t;this._monitor.report("rtc_media_statistics",n(n({direction:"up",pc_session_id:this._ctx.publishSessionId},e),{stream_id:null===(t=this._room)||void 0===t?void 0:t.getLocalStreamId(),sdk_api_name:"pusherNetStatusChange"}))},r.prototype.reportPlayerStateChange=function(e,t,n,o){var i,s;void 0===o&&(o=!1);var r=X[t];if(r&&(n+="("+r+")"),this._monitor.report("rtc_media_statistics",{direction:"down",stream_user_id:e,code:t,message:n,stream_id:this._ctx.getRemoteStreamId(e,!!o),sdk_api_name:"playerStateChange",pc_session_id:this._ctx.getSubscribeId(e,o),peer_user_type:this._ctx.getRemoteUserType(e)}),2004===t){var c=e+"_"+(null==(a=null===(i=this._room)||void 0===i?void 0:i.getStreamInfo(e))?void 0:a.streamId)+"_success";if(this._reported[c])return;this._reported[c]=!0,G(this._monitor,{event:"subscribe",streamID:(null==a?void 0:a.streamId)||"",streamAddr:(null==a?void 0:a.rtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"success",reason:n,code:t,logHost:this._ctx.getLogHost()})}else if([-2301,3001,3002,3003,3005].includes(t)){var a;c=e+"_"+(null==(a=null===(s=this._room)||void 0===s?void 0:s.getStreamInfo(e))?void 0:a.streamId)+"_failed";if(this._reported[c])return;this._reported[c]=!0,G(this._monitor,{event:"subscribe",streamID:(null==a?void 0:a.streamId)||"",streamAddr:(null==a?void 0:a.rtmp)||"",userID:this._ctx.userId,roomID:this._ctx.roomId,appID:this._ctx.appId,result:"failed",reason:n,code:t,logHost:this._ctx.getLogHost()})}},r.prototype.reportPlayerNetStatusChange=function(e,t,o){void 0===o&&(o=!1),this._monitor.report("rtc_media_statistics",n(n({direction:"down",stream_user_id:e},t),{stream_id:this._ctx.getRemoteStreamId(e,!!o),sdk_api_name:"playerNetStatusChange",pc_session_id:this._ctx.getSubscribeId(e,o),peer_user_type:this._ctx.getRemoteUserType(e)}))},r.prototype.login=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];if("string"!=typeof e)throw{code:R.INVALID_PARAMS,reason:"token: "+e+" must be a string"};if(!t||"string"!=typeof t)throw{code:R.INVALID_PARAMS,reason:"userId: "+t+" must be a string"};return this._rtsClient||(this._rtsClient=new Z(this._ctx)),this._rtsClient.login({token:e,userId:t})},r.prototype.logout=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return this._rtsClient?[4,this._rtsClient.logout()]:[2];case 1:return t.sent(),null===(e=this._rtsClient)||void 0===e||e.destroy(),delete this._rtsClient,[2]}}))}))},r.prototype.updateLoginToken=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.updateLoginToken(e)]}))}))},r.prototype.getPeerOnlineStatus=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){var t,n;return s(this,(function(o){switch(o.label){case 0:if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[4,this._rtsClient.getPeerOnlineStatus(e)];case 1:return t=o.sent(),[2,(n={},n[v.OFFLINE]=q.OFFLINE,n[v.ONLINE]=q.ONLINE,n[v.UNREACHABLE]=q.UNREACHABLE,n)[t]]}}))}))},r.prototype.sendUserMessage=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];return i(this,void 0,void 0,(function(){return s(this,(function(n){if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[2,this._room.sendUserMessage({peerUserId:e,message:t})]}))}))},r.prototype.sendUserBinaryMessage=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];return i(this,void 0,void 0,(function(){return s(this,(function(n){if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[2,this._room.sendUserMessage({peerUserId:e,message:t})]}))}))},r.prototype.sendRoomMessage=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[2,this._room.sendRoomMessage({message:e})]}))}))},r.prototype.sendRoomBinaryMessage=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!this._room||this._room.state!==N.Connected)throw{code:R.NOT_IN_ROOM,reason:"you are not in room"};return[2,this._room.sendRoomMessage({message:e})]}))}))},r.prototype.sendUserMessageOutsideRoom=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];return i(this,void 0,void 0,(function(){return s(this,(function(n){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.sendUserMessageOutsideRoom({peerUserId:e,message:t})]}))}))},r.prototype.sendUserBinaryMessageOutsideRoom=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];return i(this,void 0,void 0,(function(){return s(this,(function(n){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.sendUserMessageOutsideRoom({peerUserId:e,message:t})]}))}))},r.prototype.setServerParams=function(e,t){for(var n=[],o=2;o<arguments.length;o++)n[o-2]=arguments[o];return i(this,void 0,void 0,(function(){return s(this,(function(n){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.setServerParams({signature:e,url:t})]}))}))},r.prototype.sendServerMessage=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.sendServerMessage({message:e})]}))}))},r.prototype.sendServerBinaryMessage=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return i(this,void 0,void 0,(function(){return s(this,(function(t){if(!this._rtsClient)throw{code:R.NOT_LOGIN,reason:"you are not login"};return[2,this._rtsClient.sendServerMessage({message:e})]}))}))},Object.defineProperty(r.prototype,"state",{get:function(){var e,t;return null!==(t=null===(e=this._room)||void 0===e?void 0:e.state)&&void 0!==t?t:N.Init},enumerable:!1,configurable:!0}),o([te(),F(1)],r.prototype,"init",null),o([te(),F()],r.prototype,"destroy",null),o([te(),F(1)],r.prototype,"setBusinessId",null),o([te(),F(1)],r.prototype,"setUserVisibility",null),o([te(),F(3)],r.prototype,"join",null),o([te(),F()],r.prototype,"leave",null),o([te(),F()],r.prototype,"publish",null),o([te(),F()],r.prototype,"unpublish",null),o([te(),F(2)],r.prototype,"subscribe",null),o([te(),F(2)],r.prototype,"unsubscribe",null),o([te(),F(1)],r.prototype,"muteLocal",null),o([te(),F(2)],r.prototype,"muteRemote",null),o([te(),F(2)],r.prototype,"login",null),o([te(),F(0)],r.prototype,"logout",null),o([te(),F(1)],r.prototype,"updateLoginToken",null),o([te(),F(1)],r.prototype,"getPeerOnlineStatus",null),o([te(),F(2)],r.prototype,"sendUserMessage",null),o([te(),F(2)],r.prototype,"sendUserBinaryMessage",null),o([te(),F(1)],r.prototype,"sendRoomMessage",null),o([te(),F(1)],r.prototype,"sendRoomBinaryMessage",null),o([te(),F(2)],r.prototype,"sendUserMessageOutsideRoom",null),o([te(),F(2)],r.prototype,"sendUserBinaryMessageOutsideRoom",null),o([te(),F(2)],r.prototype,"setServerParams",null),o([te(),F(1)],r.prototype,"sendServerMessage",null),o([te(),F(1)],r.prototype,"sendServerBinaryMessage",null),r}(u.EventEmitter),EVENTS:a,ERROR_CODES:R,getSdkVersion:function(){return"3.3.0"}}}));
