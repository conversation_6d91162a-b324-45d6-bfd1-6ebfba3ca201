<template>
  <van-popup
    :show="show"
    @close="onClose"
    root-portal
    position="bottom"
    round
    closeable
    close-icon="close"
  >
    <view class="balance-pay-modal">
      <view class="balance-pay-modal-title">
        <text>充值金额（元）</text>
      </view>
      <view class="balance-pay-modal-content">
        <view class="input-section">
          <van-field
            v-model:value="rechargeAmount"
            type="digit"
            placeholder="请输入充值金额"
            border
            :maxlength="7"
            @change="onAmountInput"
            class="amount-field"
            extra-event-params
            customStyle="background: #f8f8f8;"
          />
        </view>

        <view class="fee-info">
          <text>充值手续费率1%，实充金额：{{ actualAmount }}</text>
        </view>

        <view class="confirm-btn-wrapper">
          <button class="confirm-btn" color="#1677FF" @click="onConfirm">
            确定
          </button>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { onShow } from "@dcloudio/uni-app";
const props = defineProps<{
  show: boolean;
}>();

onShow(() => {
  show.value = false;
});

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "confirm", value: { amount: number; actualAmount: number }): void;
}>();

const show = computed({
  get() {
    return props.show;
  },
  set(value) {
    emit("update:show", value);
  },
});

// 充值金额
const rechargeAmount = ref<string>("");

// 计算实际充值金额（扣除1%手续费）
const actualAmount = computed(() => {
  /** 手续费低于1分钱不扣除 */
  const amount = parseFloat(rechargeAmount.value) || 0;
  return amount < 1 ? amount.toFixed(2) : (amount * 0.99).toFixed(2);
});

// 输入金额处理
const onAmountInput = (event: any) => {
  // 获取输入值
  let value = typeof event === "string" ? event : event?.detail?.value || "";

  // 只保留数字和小数点
  value = value.replace(/[^\d.]/g, "");

  // 如果为空，直接返回
  if (!value) {
    rechargeAmount.value = "";
    event.detail.callback({ value: rechargeAmount.value });

    return;
  }

  // 限制只能有一个小数点
  const dotIndex = value.indexOf(".");
  if (dotIndex !== -1) {
    value =
      value.substring(0, dotIndex + 1) +
      value.substring(dotIndex + 1).replace(/\./g, "");
  }

  // 限制小数点后最多两位
  if (dotIndex !== -1 && value.length > dotIndex + 3) {
    value = value.substring(0, dotIndex + 3);
  }

  // 转换为数字检查是否超过10000
  const num = parseFloat(value);
  if (!isNaN(num) && num > 10000) {
    rechargeAmount.value = "10000";
    event.detail.callback({ value: rechargeAmount.value });
    uni.showToast({
      title: "最多只能输入10000元",
      icon: "none",
    });
    return;
  }

  rechargeAmount.value = value;
  event.detail.callback({ value: rechargeAmount.value });
};

// 确认充值
const onConfirm = () => {
  const amount = parseFloat(rechargeAmount.value);

  if (!rechargeAmount.value || amount <= 0) {
    uni.showToast({
      title: "请输入有效的充值金额",
      icon: "none",
    });
    return;
  }

  if (amount > 10000) {
    uni.showToast({
      title: "充值金额不能超过10000元",
      icon: "none",
    });
    return;
  }

  // 触发充值事件
  emit("confirm", {
    amount: parseFloat(rechargeAmount.value),
    actualAmount: parseFloat(actualAmount.value),
  });

  // 关闭弹窗
  emit("update:show", false);

  // 清空输入
  rechargeAmount.value = "";
};

const onClose = () => {
  emit("update:show", false);
  // 清空输入
  rechargeAmount.value = "";
};
</script>

<style lang="scss" scoped>
.balance-pay-modal {
  background-color: #fff;
  padding: 32rpx 32rpx 10rpx 32rpx;
  border-radius: 32rpx 32rpx 0 0;
  min-height: 400rpx;

  .balance-pay-modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 48rpx;
  }

  .balance-pay-modal-content {
    .input-section {
      margin-bottom: 12rpx;

      .amount-field {
        background: #f8f8f8;
        border-radius: 12rpx;

        :deep(.van-field__control) {
          font-size: 32rpx;
          color: #333;
          height: 88rpx;
          line-height: 88rpx;
        }

        :deep(.van-field__body) {
          border-radius: 12rpx;
          border: 2rpx solid #e5e5e5;

          &:focus-within {
            border-color: #1989fa;
          }
        }

        :deep(.van-field__control::placeholder) {
          color: #c8c9cc;
          font-size: 28rpx;
        }
      }
    }

    .fee-info {
      margin-bottom: 64rpx;

      text {
        font-size: 24rpx;
        color: #999;
        line-height: 1.5;
      }
    }

    .confirm-btn-wrapper {
      margin-top: 100rpx;
      .confirm-btn {
        width: 100%;
        height: 88rpx;
        background: #1989fa;
        border-radius: 44rpx;
        border: none;
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background: #1976d2;
        }

        &::after {
          border: none;
        }
      }
    }
  }
}
</style>
